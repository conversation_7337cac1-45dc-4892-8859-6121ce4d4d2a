{"version": 3, "file": "mailto.js", "sourceRoot": "", "sources": ["../../../src/schemes/mailto.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,QAAQ,CAAC;AACpE,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAa9D,MAAM,CAAC,GAAiB,EAAE,CAAC;AAC3B,MAAM,KAAK,GAAG,IAAI,CAAC;AAEnB,UAAU;AACV,MAAM,YAAY,GAAG,wBAAwB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,2EAA2E,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;AACjJ,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAE,kBAAkB;AACnD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,aAAa,GAAG,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAE,UAAU;AAE7O,qEAAqE;AACrE,yFAAyF;AACzF,+BAA+B;AAC/B,uGAAuG;AACvG,+GAA+G;AAC/G,kCAAkC;AAClC,+BAA+B;AAC/B,wGAAwG;AACxG,8EAA8E;AAC9E,8FAA8F;AAC9F,mGAAmG;AACnG,MAAM,OAAO,GAAG,uDAAuD,CAAC;AACxE,MAAM,OAAO,GAAG,4DAA4D,CAAC;AAC7E,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AAC7C,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACnF,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;AAC9C,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG,YAAY,CAAC,CAAC;AACvD,MAAM,cAAc,GAAG,MAAM,CAAC,KAAK,GAAG,SAAS,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AAE/D,UAAU;AACV,MAAM,cAAc,GAAG,0BAA0B,CAAC,CAAE,oBAAoB;AACxE,MAAM,aAAa,GAAG,qCAAqC,CAAC;AAC5D,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,GAAG,GAAG,GAAG,YAAY,GAAG,GAAG,GAAG,aAAa,CAAC,CAAC;AAC/E,MAAM,OAAO,GAAG,MAAM,CAAC,cAAc,GAAG,GAAG,GAAG,KAAK,GAAG,cAAc,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AACpF,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,GAAG,GAAG,GAAG,cAAc,CAAC,CAAC;AAClE,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC;AACzD,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC;AAClE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;AACrC,MAAM,QAAQ,GAAG,OAAO,CAAC;AACzB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAC;AACnD,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;AAClE,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC;AAC3C,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,YAAY,GAAG,GAAG,GAAG,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;AAE1E,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AACjD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AAClD,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;AACzF,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;AACrG,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9E,MAAM,WAAW,GAAG,UAAU,CAAC;AAC/B,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACvC,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC;AAElD,0BAA0B,GAAU;IACnC,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;IAChC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACnD,CAAC;AAED,MAAM,OAAO,GAAuC;IACnD,MAAM,EAAG,QAAQ;IAEjB,KAAK,EAAG,UAAU,UAAwB,EAAE,OAAkB;QAC7D,MAAM,gBAAgB,GAAG,UAA8B,CAAC;QACxD,MAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjG,gBAAgB,CAAC,IAAI,GAAG,SAAS,CAAC;QAElC,IAAI,gBAAgB,CAAC,KAAK,EAAE;YAC3B,IAAI,cAAc,GAAG,KAAK,CAAA;YAC1B,MAAM,OAAO,GAAiB,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;gBACjD,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAErC,QAAQ,MAAM,CAAC,CAAC,CAAC,EAAE;oBAClB,KAAK,IAAI;wBACR,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;4BACjD,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;yBACpB;wBACD,MAAM;oBACP,KAAK,SAAS;wBACb,gBAAgB,CAAC,OAAO,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;wBACjE,MAAM;oBACP,KAAK,MAAM;wBACV,gBAAgB,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;wBAC9D,MAAM;oBACP;wBACC,cAAc,GAAG,IAAI,CAAC;wBACtB,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;wBACvF,MAAM;iBACP;aACD;YAED,IAAI,cAAc;gBAAE,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;SACvD;QAED,gBAAgB,CAAC,KAAK,GAAG,SAAS,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;YAC5C,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE9B,IAAI,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAErC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;gBAC5B,kCAAkC;gBAClC,IAAI;oBACH,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;iBAC9E;gBAAC,OAAO,CAAC,EAAE;oBACX,gBAAgB,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,IAAI,0EAA0E,GAAG,CAAC,CAAC;iBAClI;aACD;iBAAM;gBACN,IAAI,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;aAC5D;YAED,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACvB;QAED,OAAO,gBAAgB,CAAC;IACzB,CAAC;IAED,SAAS,EAAG,UAAU,gBAAiC,EAAE,OAAkB;QAC1E,MAAM,UAAU,GAAG,gBAAiC,CAAC;QACrD,MAAM,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QACxC,IAAI,EAAE,EAAE;YACP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;gBAC5C,MAAM,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACtC,MAAM,SAAS,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;gBACxJ,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAErC,0BAA0B;gBAC1B,IAAI;oBACH,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;iBAC1H;gBAAC,OAAO,CAAC,EAAE;oBACX,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,sDAAsD,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,iBAAiB,GAAG,CAAC,CAAC;iBAC7J;gBAED,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC;aACjC;YAED,UAAU,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC/B;QAED,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,IAAI,EAAE,CAAC;QAE1E,IAAI,gBAAgB,CAAC,OAAO;YAAE,OAAO,CAAC,SAAS,CAAC,GAAG,gBAAgB,CAAC,OAAO,CAAC;QAC5E,IAAI,gBAAgB,CAAC,IAAI;YAAE,OAAO,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC;QAEnE,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YAC3B,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;gBAC9B,MAAM,CAAC,IAAI,CACV,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC;oBAC7G,GAAG;oBACH,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CACvH,CAAC;aACF;SACD;QACD,IAAI,MAAM,CAAC,MAAM,EAAE;YAClB,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACpC;QAED,OAAO,UAAU,CAAC;IACnB,CAAC;CACD,CAAA;AAED,eAAe,OAAO,CAAC"}