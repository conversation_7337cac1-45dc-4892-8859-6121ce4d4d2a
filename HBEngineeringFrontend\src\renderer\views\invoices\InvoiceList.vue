<template>
  <div class="invoice-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>發票管理</span>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            創建發票
          </el-button>
        </div>
      </template>

      <!-- 搜索和篩選 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchForm.search"
              placeholder="搜索發票號或客戶名稱"
              clearable>
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="狀態篩選" clearable>
              <el-option label="全部" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已發送" value="sent" />
              <el-option label="已付款" value="paid" />
              <el-option label="逾期" value="overdue" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="開始日期"
              end-placeholder="結束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 數據表格 -->
      <el-table :data="invoices" v-loading="loading" style="width: 100%">
        <el-table-column prop="invoice_no" label="發票號" width="150" />
        <el-table-column prop="customer_name" label="客戶" width="200" />
        <el-table-column prop="created_at" label="開票日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="due_date" label="到期日" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.due_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="final_amount" label="金額" width="120" align="right">
          <template #default="scope">
            <span style="color: #67C23A; font-weight: bold;">
              ${{ scope.row.final_amount.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="狀態" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="success" size="small" @click="handleEdit(scope.row)">
              編輯
            </el-button>
            <el-dropdown @command="(command) => handleDropdownCommand(command, scope.row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="pdf">下載PDF</el-dropdown-item>
                  <el-dropdown-item command="pay">標記為已付款</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>刪除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search, ArrowDown } from '@element-plus/icons-vue';
import dayjs from 'dayjs';

const router = useRouter();

// 數據狀態
const invoices = ref([]);
const loading = ref(false);
const showCreateDialog = ref(false);

// 分頁狀態
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索表單
const searchForm = reactive({
  search: '',
  status: '',
  dateRange: []
});

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD');
};

// 狀態類型映射
const getStatusType = (status) => {
  const statusMap = {
    'draft': 'info',
    'sent': 'warning',
    'paid': 'success',
    'overdue': 'danger'
  };
  return statusMap[status] || 'info';
};

// 狀態文本映射
const getStatusText = (status) => {
  const statusMap = {
    'draft': '草稿',
    'sent': '已發送',
    'paid': '已付款',
    'overdue': '逾期'
  };
  return statusMap[status] || status;
};

// 獲取發票列表
const fetchInvoices = async () => {
  loading.value = true;
  try {
    // 模擬API調用
    const mockData = {
      invoices: [
        {
          id: 1,
          invoice_no: 'INV-202401-0001',
          customer_name: 'ABC電子有限公司',
          created_at: '2024-01-15',
          due_date: '2024-02-15',
          final_amount: 14250.00,
          status: 'paid'
        },
        {
          id: 2,
          invoice_no: 'INV-202401-0002',
          customer_name: 'XYZ工程公司',
          created_at: '2024-01-16',
          due_date: '2024-02-16',
          final_amount: 8500.00,
          status: 'sent'
        },
        {
          id: 3,
          invoice_no: 'INV-202401-0003',
          customer_name: '香港電器有限公司',
          created_at: '2024-01-17',
          due_date: '2024-02-17',
          final_amount: 11685.00,
          status: 'draft'
        }
      ],
      total: 3,
      page: 1,
      per_page: 10
    };

    invoices.value = mockData.invoices;
    total.value = mockData.total;
    
  } catch (error) {
    ElMessage.error('獲取發票列表失敗');
  } finally {
    loading.value = false;
  }
};

// 搜索處理
const handleSearch = () => {
  currentPage.value = 1;
  fetchInvoices();
};

// 重置搜索
const handleReset = () => {
  searchForm.search = '';
  searchForm.status = '';
  searchForm.dateRange = [];
  currentPage.value = 1;
  fetchInvoices();
};

// 分頁處理
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchInvoices();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchInvoices();
};

// 查看發票
const handleView = (row) => {
  router.push(`/invoices/${row.id}`);
};

// 編輯發票
const handleEdit = (row) => {
  router.push(`/invoices/${row.id}/edit`);
};

// 下拉菜單命令處理
const handleDropdownCommand = async (command, row) => {
  switch (command) {
    case 'pdf':
      ElMessage.success(`正在下載 ${row.invoice_no} 的PDF文件`);
      break;
    case 'pay':
      await handleMarkAsPaid(row);
      break;
    case 'delete':
      await handleDelete(row);
      break;
  }
};

// 標記為已付款
const handleMarkAsPaid = async (row) => {
  try {
    await ElMessageBox.confirm(
      `確定要將發票 ${row.invoice_no} 標記為已付款嗎？`,
      '確認付款',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    ElMessage.success('標記成功');
    fetchInvoices();
  } catch {
    // 用戶取消
  }
};

// 刪除發票
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除發票 ${row.invoice_no} 嗎？此操作不可恢復。`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    ElMessage.success('刪除成功');
    fetchInvoices();
  } catch {
    // 用戶取消
  }
};

onMounted(() => {
  fetchInvoices();
});
</script>

<style scoped>
.invoice-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 