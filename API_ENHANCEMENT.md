# 蒼藍工程公司管理系統 - API 增強文檔
## H.B Engineering Company Management System - API Enhancement Documentation

### 📋 目錄

1. [新增功能概述](#新增功能概述)
2. [發票管理 API](#發票管理-api)
3. [送貨單管理 API](#送貨單管理-api)
4. [PDF 生成增強](#pdf-生成增強)
5. [統計分析 API](#統計分析-api)
6. [系統監控 API](#系統監控-api)
7. [數據備份 API](#數據備份-api)
8. [全局搜索 API](#全局搜索-api)
9. [文件上傳 API](#文件上傳-api)
10. [用戶管理增強](#用戶管理增強)
11. [產品管理完善](#產品管理完善)
12. [系統配置 API](#系統配置-api)

---

## 🚀 新增功能概述

### ✨ 主要新增功能

- **完整的發票管理系統** - 創建、查看、更新發票，支持付款狀態追蹤
- **完整的送貨單管理系統** - 創建、查看、更新送貨單，支持送貨狀態追蹤
- **文檔轉換功能** - 報價單轉發票、發票轉送貨單
- **增強的PDF生成** - 支持發票和送貨單PDF生成
- **統計分析功能** - 儀表板統計、收入分析
- **系統監控** - 實時監控系統健康狀況
- **數據備份系統** - 自動備份和手動備份功能
- **全局搜索** - 跨模塊搜索功能
- **文件上傳** - 支持文檔和圖片上傳
- **用戶管理** - 完整的用戶CRUD操作
- **產品管理完善** - 支持產品的完整生命周期管理

### 🔧 系統改進

- **錯誤處理增強** - 統一的錯誤處理機制
- **安全性提升** - JWT認證、輸入驗證、文件安全
- **性能優化** - 分頁查詢、數據緩存
- **日誌系統** - 完整的操作日誌記錄
- **配置管理** - 靈活的配置系統

---

## 📄 發票管理 API

### 1. 獲取發票列表

```http
GET /api/invoices?page=1&per_page=10&payment_status=pending
```

**查詢參數:**
- `page` (int, 可選): 頁碼，默認 1
- `per_page` (int, 可選): 每頁數量，默認 10
- `payment_status` (string, 可選): 付款狀態篩選

**響應示例:**
```json
{
  "invoices": [
    {
      "id": 1,
      "invoice_no": "INV-**********",
      "customer_name": "ABC公司",
      "final_amount": 10000.00,
      "payment_status": "pending",
      "created_at": "2023-12-01T10:00:00",
      "quotation_no": "QT-**********"
    }
  ],
  "total": 25,
  "page": 1,
  "per_page": 10,
  "success": true
}
```

### 2. 創建發票

```http
POST /api/invoices
Content-Type: application/json
```

**請求體:**
```json
{
  "customer_id": 1,
  "total_amount": 10000.00,
  "tax_rate": 0.0,
  "tax_amount": 0.0,
  "final_amount": 10000.00,
  "payment_due_date": "2024-01-01",
  "notes": "發票備註",
  "items": [
    {
      "product_id": 1,
      "description": "電力工程服務",
      "quantity": 1.0,
      "unit_price": 10000.00,
      "total_price": 10000.00
    }
  ]
}
```

### 3. 報價單轉發票

```http
POST /api/quotations/{quotation_id}/convert-to-invoice
Content-Type: application/json
```

**請求體:**
```json
{
  "tax_rate": 0.0,
  "tax_amount": 0.0,
  "payment_due_date": "2024-01-01",
  "notes": "轉換發票備註"
}
```

### 4. 更新付款狀態

```http
PUT /api/invoices/{invoice_id}/payment
Content-Type: application/json
```

**請求體:**
```json
{
  "payment_status": "paid",
  "payment_received_date": "2023-12-15"
}
```

### 5. 下載發票PDF

```http
GET /api/invoices/{invoice_id}/pdf
```

**響應:** 返回PDF文件流

---

## 🚚 送貨單管理 API

### 1. 獲取送貨單列表

```http
GET /api/delivery-notes?page=1&per_page=10&delivery_status=pending
```

**響應示例:**
```json
{
  "delivery_notes": [
    {
      "id": 1,
      "delivery_no": "DN-**********",
      "customer_name": "ABC公司",
      "delivery_status": "pending",
      "delivery_date": "2023-12-20",
      "invoice_no": "INV-**********"
    }
  ],
  "total": 15,
  "success": true
}
```

### 2. 創建送貨單

```http
POST /api/delivery-notes
Content-Type: application/json
```

**請求體:**
```json
{
  "customer_id": 1,
  "delivery_date": "2023-12-20",
  "delivery_address": "香港中環...",
  "notes": "送貨備註",
  "items": [
    {
      "product_id": 1,
      "description": "電力設備",
      "quantity": 2.0,
      "delivered_quantity": 0.0
    }
  ]
}
```

### 3. 發票轉送貨單

```http
POST /api/invoices/{invoice_id}/convert-to-delivery
Content-Type: application/json
```

**請求體:**
```json
{
  "delivery_date": "2023-12-20",
  "delivery_address": "送貨地址",
  "notes": "送貨備註"
}
```

### 4. 更新送貨狀態

```http
PUT /api/delivery-notes/{delivery_note_id}/status
Content-Type: application/json
```

**請求體:**
```json
{
  "delivery_status": "delivered",
  "received_by": "張三",
  "signature_file": "signature_filename.png"
}
```

### 5. 下載送貨單PDF

```http
GET /api/delivery-notes/{delivery_note_id}/pdf
```

---

## 📊 統計分析 API

### 1. 儀表板統計

```http
GET /api/statistics/dashboard
Authorization: Bearer {jwt_token}
```

**響應示例:**
```json
{
  "statistics": {
    "total_customers": 25,
    "active_products": 150,
    "total_quotations": 89,
    "total_invoices": 45,
    "total_delivery_notes": 32,
    "monthly_quotations": 12,
    "monthly_invoices": 8,
    "total_revenue": 250000.00,
    "monthly_revenue": 45000.00,
    "pending_quotations": 5,
    "pending_invoices": 3,
    "pending_deliveries": 2
  },
  "success": true
}
```

### 2. 收入統計

```http
GET /api/statistics/revenue
Authorization: Bearer {jwt_token}
```

**響應示例:**
```json
{
  "revenue_data": [
    {
      "month": "2023-12",
      "invoice_count": 8,
      "total_amount": 45000.00,
      "paid_amount": 35000.00
    },
    {
      "month": "2023-11",
      "invoice_count": 12,
      "total_amount": 68000.00,
      "paid_amount": 68000.00
    }
  ],
  "success": true
}
```

---

## 🔍 全局搜索 API

### 搜索功能

```http
GET /api/search?q=關鍵字&type=all
Authorization: Bearer {jwt_token}
```

**查詢參數:**
- `q` (string, 必需): 搜索關鍵字
- `type` (string, 可選): 搜索類型 (all, customers, products, quotations, invoices)

**響應示例:**
```json
{
  "query": "ABC公司",
  "results": {
    "customers": [
      {
        "id": 1,
        "name": "ABC公司",
        "contact_person": "李經理",
        "phone": "2123-4567"
      }
    ],
    "quotations": [
      {
        "id": 5,
        "quotation_no": "QT-202312-005",
        "customer_name": "ABC公司",
        "final_amount": 15000.00
      }
    ],
    "invoices": [],
    "products": []
  },
  "success": true
}
```

---

## 💾 數據備份 API

### 1. 創建備份

```http
POST /api/backup/create
Authorization: Bearer {jwt_token}
```

**響應示例:**
```json
{
  "message": "數據庫備份成功",
  "backup_file": "hb_engineering_backup_20231215_143022.db.gz",
  "success": true
}
```

### 2. 備份列表

```http
GET /api/backup/list
Authorization: Bearer {jwt_token}
```

**響應示例:**
```json
{
  "backups": [
    {
      "filename": "hb_engineering_backup_20231215_143022.db.gz",
      "size": 2048576,
      "created_at": "2023-12-15T14:30:22"
    }
  ],
  "success": true
}
```

---

## 📤 文件上傳 API

### 上傳文件

```http
POST /api/upload
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data
```

**表單數據:**
- `file`: 要上傳的文件

**響應示例:**
```json
{
  "message": "文件上傳成功",
  "filename": "abc123_document.pdf",
  "original_filename": "document.pdf",
  "success": true
}
```

---

## 👥 用戶管理增強

### 1. 獲取用戶列表（管理員）

```http
GET /api/users
Authorization: Bearer {jwt_token}
```

### 2. 更新用戶

```http
PUT /api/users/{user_id}
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**請求體:**
```json
{
  "email": "<EMAIL>",
  "role": "user",
  "password": "newpassword123"
}
```

---

## 🛍️ 產品管理完善

### 1. 獲取產品詳情

```http
GET /api/products/{product_id}
Authorization: Bearer {jwt_token}
```

### 2. 更新產品

```http
PUT /api/products/{product_id}
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**請求體:**
```json
{
  "name": "更新後的產品名稱",
  "description": "產品描述",
  "unit": "pc",
  "unit_price": 1500.00,
  "category": "電力設備"
}
```

### 3. 刪除產品（軟刪除）

```http
DELETE /api/products/{product_id}
Authorization: Bearer {jwt_token}
```

---

## ⚙️ 系統配置 API

### 獲取系統配置

```http
GET /api/config
Authorization: Bearer {jwt_token}
```

**響應示例:**
```json
{
  "config": {
    "company_name": "蒼藍工程公司",
    "company_name_en": "H.B ENGINEERING COMPANY",
    "default_currency": "HKD",
    "default_tax_rate": 0.0,
    "pagination_size": 10,
    "max_file_size": 16777216,
    "supported_languages": ["zh-HK", "en"],
    "version": "2.0.0"
  },
  "success": true
}
```

---

## 📈 數據匯出增強

### 1. 匯出客戶數據

```http
GET /api/export/customers
Authorization: Bearer {jwt_token}
```

### 2. 匯出產品數據

```http
GET /api/export/products
Authorization: Bearer {jwt_token}
```

**響應示例:**
```json
{
  "customers": [
    {
      "id": 1,
      "name": "ABC公司",
      "contact_person": "李經理",
      "phone": "2123-4567",
      "email": "<EMAIL>",
      "created_at": "2023-10-01T10:00:00"
    }
  ],
  "export_time": "2023-12-15T14:30:22",
  "success": true
}
```

---

## 🚨 錯誤處理增強

### 統一錯誤響應格式

```json
{
  "error": "錯誤描述",
  "error_code": "VALIDATION_ERROR",
  "details": {
    "field": "具體錯誤字段",
    "message": "具體錯誤信息"
  },
  "success": false,
  "timestamp": "2023-12-15T14:30:22"
}
```

### 常見錯誤代碼

- `AUTHENTICATION_ERROR` - 認證錯誤
- `AUTHORIZATION_ERROR` - 權限不足
- `VALIDATION_ERROR` - 輸入驗證錯誤
- `NOT_FOUND` - 資源不存在
- `CONFLICT` - 資源衝突
- `INTERNAL_ERROR` - 內部服務器錯誤

---

## 🔒 安全增強

### 1. JWT Token 管理
- Token 過期時間：24小時（可配置）
- 自動刷新機制
- Token 黑名單支持

### 2. 輸入驗證
- 所有用戶輸入都經過驗證和清理
- SQL 注入防護
- XSS 攻擊防護

### 3. 文件安全
- 文件類型驗證
- 文件大小限制
- 安全文件名處理

### 4. API 限流
- 按用戶和端點限流
- 防止暴力攻擊
- 可配置的限流規則

---

## 📝 使用示例

### JavaScript/TypeScript 前端整合

```javascript
// API 客戶端類
class HBEngineeringAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      },
      ...options
    };

    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'API請求失敗');
    }

    return data;
  }

  // 獲取儀表板統計
  async getDashboardStats() {
    return this.request('/api/statistics/dashboard');
  }

  // 創建發票
  async createInvoice(invoiceData) {
    return this.request('/api/invoices', {
      method: 'POST',
      body: JSON.stringify(invoiceData)
    });
  }

  // 搜索
  async search(query, type = 'all') {
    return this.request(`/api/search?q=${encodeURIComponent(query)}&type=${type}`);
  }
}

// 使用示例
const api = new HBEngineeringAPI('http://192.168.1.100:5000', 'your-jwt-token');

// 獲取統計數據
api.getDashboardStats()
  .then(stats => {
    console.log('統計數據:', stats.statistics);
  })
  .catch(error => {
    console.error('錯誤:', error.message);
  });
```

---

## 🔄 版本更新說明

### v2.0.0 更新內容

#### 新增功能
- ✅ 完整的發票管理系統
- ✅ 完整的送貨單管理系統
- ✅ 文檔轉換功能（報價單→發票→送貨單）
- ✅ 增強的PDF生成功能
- ✅ 統計分析和儀表板
- ✅ 系統監控和健康檢查
- ✅ 數據備份和恢復
- ✅ 全局搜索功能
- ✅ 文件上傳系統
- ✅ 完善的用戶管理
- ✅ 產品管理增強

#### 改進項目
- 🔧 統一的錯誤處理機制
- 🔧 增強的安全性（JWT、輸入驗證）
- 🔧 性能優化（分頁、緩存）
- 🔧 完整的日誌系統
- 🔧 靈活的配置管理
- 🔧 API文檔完善

#### 技術升級
- 🚀 Python Flask 2.3.3
- 🚀 更好的數據庫架構
- 🚀 RESTful API 設計
- 🚀 現代化的開發工具鏈

---

## 📞 技術支援

如需技術支援，請聯繫：

- **開發團隊**: 蒼藍工程公司技術部
- **電子郵件**: <EMAIL>
- **技術文檔**: 參考 `README.md` 和 `RASPBERRY_PI_SETUP.md`
- **GitHub Issue**: 提交問題和建議

---

**最後更新**: 2023年12月15日
**版本**: 2.0.0
**作者**: 蒼藍工程公司開發團隊 