<template>
  <div class="invoice-form">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h3>{{ isEdit ? $t('invoice.editInvoice') : $t('invoice.createInvoice') }}</h3>
          <div class="header-actions">
            <el-button @click="goBack" :icon="ArrowLeft">
              {{ $t('common.back') }}
            </el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="default"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="24">
          <!-- 基本信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('invoice.basicInfo') }}</h4>
          </el-col>
          
          <el-col :span="12">
            <el-form-item :label="$t('invoice.quotationId')" prop="quotationId">
              <el-select
                v-model="formData.quotationId"
                :placeholder="$t('invoice.selectQuotation')"
                style="width: 100%"
                @change="handleQuotationChange"
                :disabled="!!route.query.quotationId"
              >
                <el-option
                  v-for="quotation in quotations"
                  :key="quotation.id"
                  :value="quotation.id"
                  :label="`${quotation.quotationNumber} - ${quotation.customerName}`"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('invoice.status')" prop="status">
              <el-select
                v-model="formData.status"
                :placeholder="$t('invoice.selectStatus')"
                style="width: 100%"
              >
                <el-option
                  value="draft"
                  :label="$t('invoice.draft')"
                />
                <el-option
                  value="sent"
                  :label="$t('invoice.sent')"
                />
                <el-option
                  value="paid"
                  :label="$t('invoice.paid')"
                />
                <el-option
                  value="overdue"
                  :label="$t('invoice.overdue')"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('invoice.number')" prop="invoiceNumber">
              <el-input
                v-model="formData.invoiceNumber"
                :placeholder="$t('invoice.numberPlaceholder')"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('invoice.issueDate')" prop="issueDate">
              <el-date-picker
                v-model="formData.issueDate"
                type="date"
                :placeholder="$t('invoice.issueDatePlaceholder')"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('invoice.dueDate')" prop="dueDate">
              <el-date-picker
                v-model="formData.dueDate"
                type="date"
                :placeholder="$t('invoice.dueDatePlaceholder')"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('invoice.paymentMethod')" prop="paymentMethod">
              <el-select
                v-model="formData.paymentMethod"
                :placeholder="$t('invoice.selectPaymentMethod')"
                style="width: 100%"
              >
                <el-option value="bank_transfer" :label="$t('invoice.bankTransfer')" />
                <el-option value="cash" :label="$t('invoice.cash')" />
                <el-option value="cheque" :label="$t('invoice.cheque')" />
                <el-option value="credit_card" :label="$t('invoice.creditCard')" />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 客戶信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('invoice.customerInfo') }}</h4>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.name')" prop="customerName">
              <el-input
                v-model="formData.customerName"
                :placeholder="$t('customer.name')"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.email')" prop="customerEmail">
              <el-input
                v-model="formData.customerEmail"
                :placeholder="$t('customer.email')"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.phone')" prop="customerPhone">
              <el-input
                v-model="formData.customerPhone"
                :placeholder="$t('customer.phone')"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.address')" prop="customerAddress">
              <el-input
                v-model="formData.customerAddress"
                :placeholder="$t('customer.address')"
                type="textarea"
                :rows="2"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <!-- 發票項目 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('invoice.items') }}</h4>
          </el-col>

          <el-col :span="24">
            <div class="items-table">
              <el-table
                :data="formData.items"
                border
                size="small"
                style="width: 100%"
                max-height="300"
              >
                <el-table-column
                  prop="productName"
                  :label="$t('product.name')"
                  min-width="150"
                />
                <el-table-column
                  prop="description"
                  :label="$t('product.description')"
                  min-width="200"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="quantity"
                  :label="$t('quotation.quantity')"
                  width="100"
                  align="center"
                />
                <el-table-column
                  prop="unitPrice"
                  :label="$t('quotation.unitPrice')"
                  width="120"
                  align="right"
                >
                  <template #default="{ row }">
                    HK$ {{ formatCurrency(row.unitPrice) }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="totalPrice"
                  :label="$t('quotation.totalPrice')"
                  width="120"
                  align="right"
                >
                  <template #default="{ row }">
                    HK$ {{ formatCurrency(row.totalPrice) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>

          <!-- 金額摘要 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('invoice.summary') }}</h4>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('invoice.subtotal')" prop="subtotal">
              <el-input
                v-model="formData.subtotal"
                :disabled="true"
                :formatter="formatCurrency"
              >
                <template #prepend>HK$</template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('invoice.taxAmount')" prop="taxAmount">
              <el-input
                v-model="formData.taxAmount"
                :disabled="true"
                :formatter="formatCurrency"
              >
                <template #prepend>HK$</template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('invoice.totalAmount')" prop="totalAmount">
              <el-input
                v-model="formData.totalAmount"
                :disabled="true"
                :formatter="formatCurrency"
                style="font-weight: bold;"
              >
                <template #prepend>HK$</template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('invoice.notes')" prop="notes">
              <el-input
                v-model="formData.notes"
                :placeholder="$t('invoice.notesPlaceholder')"
                type="textarea"
                :rows="4"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 表單操作按鈕 -->
        <div class="form-actions">
          <el-button @click="goBack">
            {{ $t('common.cancel') }}
          </el-button>
          <el-button @click="handlePreview" :icon="View">
            {{ $t('invoice.preview') }}
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ $t('common.save') }}
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { ArrowLeft, View } from '@element-plus/icons-vue';
import { useQuotationStore } from '@/stores/quotation';
import { formatCurrency, validationRules } from '@/utils';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const quotationStore = useQuotationStore();

// 表單引用
const formRef = ref(null);

// 狀態
const submitting = ref(false);
const isEdit = computed(() => !!route.params.id);

// 已批准的報價單列表
const quotations = computed(() => 
  quotationStore.quotations.filter(q => q.status === 'approved')
);

// 表單數據
const formData = reactive({
  quotationId: '',
  status: 'draft',
  invoiceNumber: '',
  issueDate: '',
  dueDate: '',
  paymentMethod: 'bank_transfer',
  customerName: '',
  customerEmail: '',
  customerPhone: '',
  customerAddress: '',
  items: [],
  subtotal: 0,
  taxAmount: 0,
  totalAmount: 0,
  notes: ''
});

// 表單驗證規則
const formRules = computed(() => ({
  quotationId: [validationRules.required()],
  status: [validationRules.required()],
  invoiceNumber: [
    validationRules.required(),
    validationRules.minLength(3),
    validationRules.maxLength(20)
  ],
  issueDate: [validationRules.required()],
  dueDate: [
    validationRules.required(),
    {
      validator: (rule, value, callback) => {
        if (value && formData.issueDate && new Date(value) <= new Date(formData.issueDate)) {
          callback(new Error(t('invoice.dueDateError')));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  paymentMethod: [validationRules.required()],
  notes: [validationRules.maxLength(1000)]
}));

// 處理報價單變更
const handleQuotationChange = async (quotationId) => {
  if (!quotationId) return;

  const quotation = quotations.value.find(q => q.id === quotationId);
  if (quotation) {
    // 填充客戶信息
    formData.customerName = quotation.customerName;
    formData.customerEmail = quotation.customerEmail;
    formData.customerPhone = quotation.customerPhone;
    formData.customerAddress = quotation.customerAddress;
    
    // 複製項目
    formData.items = [...quotation.items];
    
    // 計算金額
    calculateAmounts();
  }
};

// 計算金額
const calculateAmounts = () => {
  const subtotal = formData.items.reduce((sum, item) => sum + item.totalPrice, 0);
  const taxAmount = subtotal * 0.05; // 5% 稅率
  const totalAmount = subtotal + taxAmount;
  
  formData.subtotal = subtotal;
  formData.taxAmount = taxAmount;
  formData.totalAmount = totalAmount;
};

// 預覽發票
const handlePreview = () => {
  // TODO: 實現發票預覽功能
  ElMessage.info(t('invoice.previewFeature'));
};

// 返回列表頁
const goBack = () => {
  router.push('/invoices');
};

// 提交表單
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    // TODO: 實現發票保存邏輯
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success(
      isEdit.value ? t('invoice.updateSuccess') : t('invoice.createSuccess')
    );
    goBack();
  } catch (error) {
    console.error('Form validation failed:', error);
  } finally {
    submitting.value = false;
  }
};

// 自動生成發票號碼
const generateInvoiceNumber = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const time = String(now.getTime()).slice(-6);
  return `INV${year}${month}${day}${time}`;
};

// 設置默認日期
const setDefaultDates = () => {
  const today = new Date();
  const dueDate = new Date(today);
  dueDate.setDate(today.getDate() + 30); // 30天後到期

  formData.issueDate = today.toISOString().split('T')[0];
  formData.dueDate = dueDate.toISOString().split('T')[0];
};

onMounted(() => {
  // 從查詢參數獲取報價單ID
  if (route.query.quotationId) {
    formData.quotationId = route.query.quotationId;
    handleQuotationChange(route.query.quotationId);
  }

  // 設置默認值
  if (!isEdit.value) {
    formData.invoiceNumber = generateInvoiceNumber();
    setDefaultDates();
  }
});
</script>

<style scoped>
.invoice-form {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.section-title {
  color: #409EFF;
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.items-table {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.form-actions .el-button {
  min-width: 120px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .invoice-form {
    padding: 12px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style> 