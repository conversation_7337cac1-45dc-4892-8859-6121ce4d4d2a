# 蒼藍工程公司管理系統

這是一個基於 Electron + Vue 3 + Element Plus 的桌面應用程序，用於管理工程公司的業務流程。

## 功能特性

### 📊 儀表板
- 業務統計概覽
- 最近報價單和發票列表
- 月度業務統計圖表

### 📋 報價單管理
- 創建、編輯、查看報價單
- 產品項目管理
- 自動計算金額和折扣
- 轉換為發票或送貨單
- PDF 導出功能

### 🧾 發票管理
- 發票創建和管理
- 付款狀態跟踪
- 逾期提醒
- PDF 導出功能

### 🚚 送貨單管理
- 送貨單創建和跟踪
- 簽收狀態管理
- 電子簽名功能

### 👥 客戶管理
- 客戶信息維護
- 聯絡人管理
- 公司註冊信息

### 📦 產品管理
- 產品目錄維護
- 分類管理
- 價格管理
- 庫存狀態

## 技術棧

- **前端框架**: Vue 3 (Composition API)
- **UI 組件庫**: Element Plus
- **桌面應用**: Electron
- **構建工具**: Vite
- **狀態管理**: Pinia
- **路由**: Vue Router
- **國際化**: Vue I18n
- **樣式**: CSS3 + Sass

## 開發環境設置

### 前置要求
- Node.js 16+ 
- npm 或 yarn

### 安裝依賴
```bash
npm install
```

### 開發模式運行

1. 啟動 Vite 開發服務器：
```bash
npm run dev:vite
```

2. 在另一個終端啟動 Electron：
```bash
npm run start
```

或者使用並發模式（需要安裝 concurrently）：
```bash
npm run dev
```

### 構建應用

構建 Web 資源：
```bash
npm run build
```

打包 Electron 應用：
```bash
# Windows
npm run build:win

# macOS
npm run build:mac

# Linux
npm run build:linux
```

## 項目結構

```
HBEngineeringFrontend/
├── src/
│   ├── api/                 # API 接口定義
│   │   ├── client.js       # HTTP 客戶端配置
│   │   └── index.js        # API 接口
│   └── renderer/           # 渲染進程代碼
│       ├── components/     # 公共組件
│       ├── layouts/        # 布局組件
│       ├── views/          # 頁面組件
│       │   ├── Dashboard.vue
│       │   ├── Login.vue
│       │   ├── customers/
│       │   ├── products/
│       │   ├── quotations/
│       │   ├── invoices/
│       │   └── delivery-notes/
│       ├── stores/         # Pinia 狀態管理
│       ├── router/         # 路由配置
│       ├── i18n/          # 國際化配置
│       ├── styles/        # 全局樣式
│       ├── App.vue        # 根組件
│       ├── main.js        # 應用入口
│       └── index.html     # HTML 模板
├── main.js                # Electron 主進程
├── package.json
├── vite.config.js        # Vite 配置
└── README.md
```

## 使用說明

### 登入系統
- 用戶名：任意
- 密碼：任意
- 系統會自動模擬登入成功

### 導航菜單
- **儀表板**: 查看業務概覽和統計
- **報價單管理**: 管理所有報價單
- **發票管理**: 管理發票和付款狀態
- **送貨單管理**: 管理送貨和簽收
- **客戶管理**: 維護客戶信息
- **產品管理**: 管理產品目錄

### 數據管理
目前使用模擬數據，實際部署時需要連接到後端 API。

## 配置說明

### API 配置
在 `src/api/client.js` 中配置 API 基礎 URL：
```javascript
const apiClient = axios.create({
  baseURL: '/api', // 修改為實際的 API 地址
  timeout: 10000
});
```

### 代理配置
在 `vite.config.js` 中配置開發代理：
```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:5000', // 後端服務地址
      changeOrigin: true
    }
  }
}
```

## 部署說明

### 開發部署
1. 確保後端 API 服務正在運行
2. 修改 API 配置指向正確的後端地址
3. 運行 `npm run dev`

### 生產部署
1. 構建前端資源：`npm run build`
2. 打包 Electron 應用：`npm run build:win`
3. 分發生成的安裝包

## 故障排除

### 常見問題

1. **Electron 窗口空白**
   - 檢查 Vite 開發服務器是否正在運行
   - 確認端口 3000 沒有被占用

2. **API 請求失敗**
   - 檢查後端服務是否啟動
   - 確認 API 代理配置正確

3. **構建失敗**
   - 清除 node_modules 並重新安裝
   - 檢查 Node.js 版本是否符合要求

### 開發工具
- 按 F12 打開開發者工具
- 在開發模式下會自動打開 DevTools

## 貢獻指南

1. Fork 項目
2. 創建功能分支
3. 提交更改
4. 推送到分支
5. 創建 Pull Request

## 許可證

本項目採用 MIT 許可證。

## 聯絡方式

如有問題或建議，請聯絡開發團隊。 