<template>
  <div class="chart-card">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-actions" v-if="$slots.actions">
        <slot name="actions" />
      </div>
    </div>
    
    <div class="chart-content">
      <div v-if="loading" class="chart-loading">
        <el-skeleton :rows="4" animated />
      </div>
      <div v-else-if="!data || data.length === 0" class="chart-empty">
        <el-empty :description="$t('common.noData')" />
      </div>
      <div v-else class="chart-container">
        <canvas
          ref="chartCanvas"
          :width="canvasWidth"
          :height="canvasHeight"
          @mousemove="handleMouseMove"
          @mouseleave="handleMouseLeave"
        />
        <div v-if="tooltip.show" class="chart-tooltip" :style="tooltipStyle">
          <div class="tooltip-content">
            <div class="tooltip-title">{{ tooltip.title }}</div>
            <div class="tooltip-value">{{ tooltip.value }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="chart-legend" v-if="showLegend && legendData.length > 0">
      <div
        v-for="(item, index) in legendData"
        :key="index"
        class="legend-item"
      >
        <div
          class="legend-color"
          :style="{ backgroundColor: item.color }"
        />
        <span class="legend-label">{{ item.label }}</span>
        <span class="legend-value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { formatCurrency, formatNumber } from '@/utils';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
  type: {
    type: String,
    default: 'line', // line, bar, pie, doughnut
    validator: (value) => ['line', 'bar', 'pie', 'doughnut'].includes(value)
  },
  loading: {
    type: Boolean,
    default: false
  },
  height: {
    type: Number,
    default: 300
  },
  colors: {
    type: Array,
    default: () => ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  valueType: {
    type: String,
    default: 'number', // number, currency, percentage
    validator: (value) => ['number', 'currency', 'percentage'].includes(value)
  }
});

const chartCanvas = ref(null);
const canvasWidth = ref(600);
const canvasHeight = ref(300);
const tooltip = ref({
  show: false,
  x: 0,
  y: 0,
  title: '',
  value: ''
});

// 圖例數據
const legendData = computed(() => {
  if (!props.data || props.data.length === 0) return [];
  
  return props.data.map((item, index) => ({
    label: item.label || item.name,
    value: formatValue(item.value),
    color: props.colors[index % props.colors.length]
  }));
});

// 提示框樣式
const tooltipStyle = computed(() => ({
  left: `${tooltip.value.x}px`,
  top: `${tooltip.value.y}px`
}));

// 格式化值
const formatValue = (value) => {
  switch (props.valueType) {
    case 'currency':
      return formatCurrency(value);
    case 'percentage':
      return `${formatNumber(value, 1)}%`;
    case 'number':
    default:
      return formatNumber(value, 0);
  }
};

// 繪製圖表
const drawChart = () => {
  if (!chartCanvas.value || !props.data || props.data.length === 0) return;
  
  const canvas = chartCanvas.value;
  const ctx = canvas.getContext('2d');
  const { width, height } = canvas;
  
  // 清空畫布
  ctx.clearRect(0, 0, width, height);
  
  switch (props.type) {
    case 'line':
      drawLineChart(ctx, width, height);
      break;
    case 'bar':
      drawBarChart(ctx, width, height);
      break;
    case 'pie':
      drawPieChart(ctx, width, height);
      break;
    case 'doughnut':
      drawDoughnutChart(ctx, width, height);
      break;
  }
};

// 繪製折線圖
const drawLineChart = (ctx, width, height) => {
  const padding = 40;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;
  
  const values = props.data.map(item => item.value);
  const maxValue = Math.max(...values);
  const minValue = Math.min(...values);
  const valueRange = maxValue - minValue || 1;
  
  // 繪製網格線
  ctx.strokeStyle = '#f0f0f0';
  ctx.lineWidth = 1;
  
  // 水平網格線
  for (let i = 0; i <= 5; i++) {
    const y = padding + (chartHeight / 5) * i;
    ctx.beginPath();
    ctx.moveTo(padding, y);
    ctx.lineTo(width - padding, y);
    ctx.stroke();
  }
  
  // 垂直網格線
  const stepX = chartWidth / (props.data.length - 1);
  for (let i = 0; i < props.data.length; i++) {
    const x = padding + stepX * i;
    ctx.beginPath();
    ctx.moveTo(x, padding);
    ctx.lineTo(x, height - padding);
    ctx.stroke();
  }
  
  // 繪製折線
  ctx.strokeStyle = props.colors[0];
  ctx.lineWidth = 2;
  ctx.beginPath();
  
  props.data.forEach((item, index) => {
    const x = padding + stepX * index;
    const y = height - padding - ((item.value - minValue) / valueRange) * chartHeight;
    
    if (index === 0) {
      ctx.moveTo(x, y);
    } else {
      ctx.lineTo(x, y);
    }
  });
  
  ctx.stroke();
  
  // 繪製數據點
  ctx.fillStyle = props.colors[0];
  props.data.forEach((item, index) => {
    const x = padding + stepX * index;
    const y = height - padding - ((item.value - minValue) / valueRange) * chartHeight;
    
    ctx.beginPath();
    ctx.arc(x, y, 4, 0, 2 * Math.PI);
    ctx.fill();
  });
};

// 繪製柱狀圖
const drawBarChart = (ctx, width, height) => {
  const padding = 40;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;
  
  const values = props.data.map(item => item.value);
  const maxValue = Math.max(...values);
  
  const barWidth = chartWidth / props.data.length * 0.8;
  const barSpacing = chartWidth / props.data.length * 0.2;
  
  props.data.forEach((item, index) => {
    const barHeight = (item.value / maxValue) * chartHeight;
    const x = padding + (barWidth + barSpacing) * index + barSpacing / 2;
    const y = height - padding - barHeight;
    
    ctx.fillStyle = props.colors[index % props.colors.length];
    ctx.fillRect(x, y, barWidth, barHeight);
  });
};

// 繪製餅圖
const drawPieChart = (ctx, width, height) => {
  const centerX = width / 2;
  const centerY = height / 2;
  const radius = Math.min(width, height) / 2 - 20;
  
  const total = props.data.reduce((sum, item) => sum + item.value, 0);
  let currentAngle = -Math.PI / 2;
  
  props.data.forEach((item, index) => {
    const sliceAngle = (item.value / total) * 2 * Math.PI;
    
    ctx.fillStyle = props.colors[index % props.colors.length];
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
    ctx.closePath();
    ctx.fill();
    
    currentAngle += sliceAngle;
  });
};

// 繪製環形圖
const drawDoughnutChart = (ctx, width, height) => {
  const centerX = width / 2;
  const centerY = height / 2;
  const outerRadius = Math.min(width, height) / 2 - 20;
  const innerRadius = outerRadius * 0.5;
  
  const total = props.data.reduce((sum, item) => sum + item.value, 0);
  let currentAngle = -Math.PI / 2;
  
  props.data.forEach((item, index) => {
    const sliceAngle = (item.value / total) * 2 * Math.PI;
    
    ctx.fillStyle = props.colors[index % props.colors.length];
    ctx.beginPath();
    ctx.arc(centerX, centerY, outerRadius, currentAngle, currentAngle + sliceAngle);
    ctx.arc(centerX, centerY, innerRadius, currentAngle + sliceAngle, currentAngle, true);
    ctx.closePath();
    ctx.fill();
    
    currentAngle += sliceAngle;
  });
};

// 鼠標移動事件
const handleMouseMove = (event) => {
  const rect = chartCanvas.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  
  // 簡化的提示框邏輯，實際應用中需要根據圖表類型計算具體的數據點
  const dataIndex = Math.floor((x - 40) / ((canvasWidth.value - 80) / props.data.length));
  
  if (dataIndex >= 0 && dataIndex < props.data.length) {
    const item = props.data[dataIndex];
    tooltip.value = {
      show: true,
      x: event.clientX - rect.left + 10,
      y: event.clientY - rect.top - 10,
      title: item.label || item.name,
      value: formatValue(item.value)
    };
  }
};

// 鼠標離開事件
const handleMouseLeave = () => {
  tooltip.value.show = false;
};

// 響應式處理
const handleResize = () => {
  if (chartCanvas.value) {
    const container = chartCanvas.value.parentElement;
    canvasWidth.value = container.offsetWidth;
    canvasHeight.value = props.height;
    nextTick(() => {
      drawChart();
    });
  }
};

// 監聽數據變化
watch(() => props.data, () => {
  nextTick(() => {
    drawChart();
  });
}, { deep: true });

// 監聽類型變化
watch(() => props.type, () => {
  nextTick(() => {
    drawChart();
  });
});

onMounted(() => {
  handleResize();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.chart-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #f0f0f0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-content {
  position: relative;
  margin-bottom: 20px;
}

.chart-loading,
.chart-empty {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 10;
}

.tooltip-content {
  white-space: nowrap;
}

.tooltip-title {
  font-weight: 500;
  margin-bottom: 2px;
}

.tooltip-value {
  font-size: 14px;
  font-weight: bold;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-label {
  color: #666;
}

.legend-value {
  color: #333;
  font-weight: 500;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .chart-card {
    padding: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .chart-legend {
    justify-content: center;
  }
}
</style> 