import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  escapeHtml,
  unescapeHtml,
  stripHtml,
  isValidUrl,
  isSafeRedirectUrl,
  generateRandomString,
  generateUUID,
  checkPasswordStrength,
  InputValidator,
  SecureStorage,
  RateLimiter,
} from '@/utils/security';

describe('Security Utils', () => {
  describe('HTML 處理', () => {
    it('escapeHtml 應該正確轉義 HTML 字符', () => {
      expect(escapeHtml('<script>alert("xss")</script>')).toBe(
        '&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;'
      );
      expect(escapeHtml('Hello & "World"')).toBe('Hello &amp; &quot;World&quot;');
      expect(escapeHtml("It's a test")).toBe('It&#039;s a test');
    });

    it('unescapeHtml 應該正確反轉義 HTML 字符', () => {
      expect(unescapeHtml('&lt;div&gt;Hello&lt;&#x2F;div&gt;')).toBe('<div>Hello</div>');
      expect(unescapeHtml('Hello &amp; &quot;World&quot;')).toBe('Hello & "World"');
    });

    it('stripHtml 應該移除所有 HTML 標籤', () => {
      expect(stripHtml('<p>Hello <strong>World</strong></p>')).toBe('Hello World');
      expect(stripHtml('<script>alert("xss")</script>Text')).toBe('alert("xss")Text');
      expect(stripHtml('No HTML here')).toBe('No HTML here');
    });
  });

  describe('URL 驗證', () => {
    it('isValidUrl 應該正確驗證 URL', () => {
      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('http://localhost:3000')).toBe(true);
      expect(isValidUrl('ftp://example.com')).toBe(false);
      expect(isValidUrl('javascript:alert(1)')).toBe(false);
      expect(isValidUrl('not-a-url')).toBe(false);
    });

    it('isSafeRedirectUrl 應該正確驗證重定向 URL', () => {
      // Mock window.location
      Object.defineProperty(window, 'location', {
        value: { hostname: 'example.com' },
        writable: true,
      });

      expect(isSafeRedirectUrl('https://example.com/page')).toBe(true);
      expect(isSafeRedirectUrl('https://evil.com/page')).toBe(false);
      expect(isSafeRedirectUrl('https://trusted.com/page', ['trusted.com'])).toBe(true);
      expect(isSafeRedirectUrl('javascript:alert(1)')).toBe(false);
    });
  });

  describe('隨機字符串生成', () => {
    it('generateRandomString 應該生成指定長度的隨機字符串', () => {
      const str1 = generateRandomString(10);
      const str2 = generateRandomString(10);
      
      expect(str1).toHaveLength(10);
      expect(str2).toHaveLength(10);
      expect(str1).not.toBe(str2); // 應該是不同的
      expect(/^[A-Za-z0-9]+$/.test(str1)).toBe(true); // 只包含字母和數字
    });

    it('generateUUID 應該生成有效的 UUID', () => {
      const uuid1 = generateUUID();
      const uuid2 = generateUUID();
      
      expect(uuid1).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/);
      expect(uuid1).not.toBe(uuid2);
    });
  });

  describe('密碼強度檢查', () => {
    it('應該正確評估弱密碼', () => {
      const result = checkPasswordStrength('123');
      expect(result.score).toBe(0);
      expect(result.isValid).toBe(false);
      expect(result.feedback).toContain('密碼長度至少需要8個字符');
    });

    it('應該正確評估中等強度密碼', () => {
      const result = checkPasswordStrength('password123');
      expect(result.score).toBeGreaterThan(0);
      expect(result.feedback).toContain('密碼應包含大寫字母');
      expect(result.feedback).toContain('密碼應包含特殊字符');
    });

    it('應該正確評估強密碼', () => {
      const result = checkPasswordStrength('MySecure123!');
      expect(result.score).toBeGreaterThanOrEqual(3);
      expect(result.isValid).toBe(true);
      expect(result.feedback).toHaveLength(0);
    });

    it('應該檢測常見密碼', () => {
      const result = checkPasswordStrength('password');
      expect(result.score).toBe(0);
      expect(result.feedback).toContain('請避免使用常見密碼');
    });
  });

  describe('InputValidator', () => {
    it('isValidEmail 應該正確驗證郵箱', () => {
      expect(InputValidator.isValidEmail('<EMAIL>')).toBe(true);
      expect(InputValidator.isValidEmail('<EMAIL>')).toBe(true);
      expect(InputValidator.isValidEmail('invalid-email')).toBe(false);
      expect(InputValidator.isValidEmail('test@')).toBe(false);
    });

    it('isValidPhone 應該正確驗證手機號', () => {
      expect(InputValidator.isValidPhone('13812345678')).toBe(true);
      expect(InputValidator.isValidPhone('15987654321')).toBe(true);
      expect(InputValidator.isValidPhone('12345678901')).toBe(false);
      expect(InputValidator.isValidPhone('1381234567')).toBe(false);
    });

    it('isValidIdCard 應該正確驗證身份證號', () => {
      expect(InputValidator.isValidIdCard('123456789012345678')).toBe(true);
      expect(InputValidator.isValidIdCard('12345678901234567X')).toBe(true);
      expect(InputValidator.isValidIdCard('123456789012345')).toBe(true);
      expect(InputValidator.isValidIdCard('12345')).toBe(false);
    });

    it('isInRange 應該正確驗證數字範圍', () => {
      expect(InputValidator.isInRange(5, 1, 10)).toBe(true);
      expect(InputValidator.isInRange(1, 1, 10)).toBe(true);
      expect(InputValidator.isInRange(10, 1, 10)).toBe(true);
      expect(InputValidator.isInRange(0, 1, 10)).toBe(false);
      expect(InputValidator.isInRange(11, 1, 10)).toBe(false);
    });

    it('isValidLength 應該正確驗證字符串長度', () => {
      expect(InputValidator.isValidLength('hello', 3, 10)).toBe(true);
      expect(InputValidator.isValidLength('hi', 3, 10)).toBe(false);
      expect(InputValidator.isValidLength('very long string', 3, 10)).toBe(false);
    });

    it('isAlphanumeric 應該正確驗證字母數字字符串', () => {
      expect(InputValidator.isAlphanumeric('abc123')).toBe(true);
      expect(InputValidator.isAlphanumeric('ABC')).toBe(true);
      expect(InputValidator.isAlphanumeric('123')).toBe(true);
      expect(InputValidator.isAlphanumeric('abc-123')).toBe(false);
      expect(InputValidator.isAlphanumeric('abc 123')).toBe(false);
    });

    it('isPositiveInteger 應該正確驗證正整數', () => {
      expect(InputValidator.isPositiveInteger(5)).toBe(true);
      expect(InputValidator.isPositiveInteger('10')).toBe(true);
      expect(InputValidator.isPositiveInteger(0)).toBe(false);
      expect(InputValidator.isPositiveInteger(-5)).toBe(false);
      expect(InputValidator.isPositiveInteger(3.14)).toBe(false);
      expect(InputValidator.isPositiveInteger('abc')).toBe(false);
    });
  });

  describe('SecureStorage', () => {
    beforeEach(() => {
      localStorage.clear();
    });

    it('應該能夠存儲和讀取數據', () => {
      const testData = { name: 'test', value: 123 };
      
      SecureStorage.setItem('test-key', testData);
      const retrieved = SecureStorage.getItem('test-key', null);
      
      expect(retrieved).toEqual(testData);
    });

    it('不存在的鍵應該返回默認值', () => {
      const defaultValue = { default: true };
      const result = SecureStorage.getItem('nonexistent', defaultValue);
      
      expect(result).toEqual(defaultValue);
    });

    it('應該能夠移除數據', () => {
      SecureStorage.setItem('test-key', 'test-value');
      SecureStorage.removeItem('test-key');
      
      const result = SecureStorage.getItem('test-key', 'default');
      expect(result).toBe('default');
    });

    it('應該能夠清空所有數據', () => {
      SecureStorage.setItem('key1', 'value1');
      SecureStorage.setItem('key2', 'value2');
      SecureStorage.clear();
      
      expect(SecureStorage.getItem('key1', 'default')).toBe('default');
      expect(SecureStorage.getItem('key2', 'default')).toBe('default');
    });
  });

  describe('RateLimiter', () => {
    beforeEach(() => {
      RateLimiter.resetAll();
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('應該允許在限制內的請求', () => {
      expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(true);
      expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(true);
      expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(true);
    });

    it('應該拒絕超出限制的請求', () => {
      // 發送 5 個請求（達到限制）
      for (let i = 0; i < 5; i++) {
        expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(true);
      }
      
      // 第 6 個請求應該被拒絕
      expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(false);
    });

    it('應該在時間窗口過期後重置限制', () => {
      // 發送 5 個請求（達到限制）
      for (let i = 0; i < 5; i++) {
        expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(true);
      }
      
      // 超出限制
      expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(false);
      
      // 時間前進 61 秒
      vi.advanceTimersByTime(61000);
      
      // 現在應該允許新的請求
      expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(true);
    });

    it('不同的鍵應該有獨立的限制', () => {
      // key1 達到限制
      for (let i = 0; i < 5; i++) {
        expect(RateLimiter.isAllowed('key1', 5, 60000)).toBe(true);
      }
      expect(RateLimiter.isAllowed('key1', 5, 60000)).toBe(false);
      
      // key2 應該仍然可用
      expect(RateLimiter.isAllowed('key2', 5, 60000)).toBe(true);
    });

    it('reset 應該清除特定鍵的限制', () => {
      // 達到限制
      for (let i = 0; i < 5; i++) {
        expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(true);
      }
      expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(false);
      
      // 重置
      RateLimiter.reset('test-key');
      
      // 現在應該允許請求
      expect(RateLimiter.isAllowed('test-key', 5, 60000)).toBe(true);
    });
  });
});
