import { defineStore } from 'pinia';
import { ref } from 'vue';
import axios from 'axios';

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '');
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'));

  const login = async (credentials) => {
    try {
      const response = await axios.post('/api/auth/login', credentials);
      const { access_token, user } = response.data;
      
      token.value = access_token;
      userInfo.value = user;
      
      localStorage.setItem('token', access_token);
      localStorage.setItem('userInfo', JSON.stringify(user));
      
      // 設置 axios 默認 header
      axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
    } catch (error) {
      throw new Error(error.response?.data?.error || '登入失敗');
    }
  };

  const logout = () => {
    token.value = '';
    userInfo.value = {};
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    delete axios.defaults.headers.common['Authorization'];
  };

  const updateUserInfo = (info) => {
    userInfo.value = { ...userInfo.value, ...info };
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value));
  };

  // 添加直接設置方法
  const setToken = (newToken) => {
    token.value = newToken;
    localStorage.setItem('token', newToken);
    if (newToken) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
    }
  };

  const setUserInfo = (info) => {
    userInfo.value = info;
    localStorage.setItem('userInfo', JSON.stringify(info));
  };

  // 檢查是否已登入
  const isLoggedIn = () => {
    return !!token.value;
  };

  // 初始化時設置 token
  if (token.value) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`;
  }

  return {
    token,
    userInfo,
    login,
    logout,
    updateUserInfo,
    setToken,
    setUserInfo,
    isLoggedIn
  };
}); 