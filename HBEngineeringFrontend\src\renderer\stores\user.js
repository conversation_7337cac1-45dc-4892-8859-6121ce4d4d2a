import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { authAPI } from '../../api/simple-client.js';

export const useUserStore = defineStore('user', () => {
  // 狀態
  const token = ref('');
  const refreshToken = ref('');
  const userInfo = ref(null);
  const permissions = ref([]);
  const lastLoginTime = ref(0);
  const isLoading = ref(false);

  // 計算屬性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value);
  const userName = computed(() => userInfo.value?.name || '');
  const userRole = computed(() => userInfo.value?.role || '');
  const userAvatar = computed(() => userInfo.value?.avatar || '');
  const isAdmin = computed(() => userInfo.value?.role === 'admin');
  const isSuperAdmin = computed(() => userInfo.value?.role === 'super_admin');

  // 權限檢查
  const hasPermission = (permission) => {
    if (isSuperAdmin.value) return true;
    return permissions.value.some(p => p.code === permission && p.enabled);
  };

  const hasAnyPermission = (permissionList) => {
    return permissionList.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissionList) => {
    return permissionList.every(permission => hasPermission(permission));
  };

  // 登入
  const login = async (credentials) => {
    try {
      isLoading.value = true;

      const response = await authAPI.login(credentials);
      const { access_token, refresh_token, user, permissions: userPermissions } = response.data;

      // 更新狀態
      token.value = access_token;
      refreshToken.value = refresh_token;
      userInfo.value = user;
      permissions.value = userPermissions || [];
      lastLoginTime.value = Date.now();

      // 記錄登入日誌
      console.log(`User ${user.name} logged in at ${new Date().toISOString()}`);

    } catch (error) {
      console.error('Login failed:', error);
      throw new Error(error.response?.data?.error || '登入失敗');
    } finally {
      isLoading.value = false;
    }
  };

  // 登出
  const logout = async (silent = false) => {
    try {
      if (!silent && token.value) {
        // 調用後端登出 API
        await authAPI.logout().catch(() => {
          // 忽略登出 API 錯誤，繼續清除本地狀態
        });
      }
    } finally {
      // 清除狀態
      token.value = '';
      refreshToken.value = '';
      userInfo.value = null;
      permissions.value = [];
      lastLoginTime.value = 0;

      console.log('User logged out');
    }
  };

  // 刷新用戶信息
  const refreshUserInfo = async () => {
    try {
      if (!token.value) return;

      const response = await authAPI.getUserInfo();
      userInfo.value = response.data.user;
      permissions.value = response.data.permissions || [];

    } catch (error) {
      console.error('Failed to refresh user info:', error);
      // 如果刷新失敗，可能是 token 過期，執行登出
      await logout(true);
    }
  };

  // 更新用戶信息
  const updateUserInfo = async (info) => {
    try {
      userInfo.value = { ...userInfo.value, ...info };

    } catch (error) {
      console.error('Failed to update user info:', error);
      throw error;
    }
  };

  // 修改密碼
  const changePassword = async (oldPassword, newPassword) => {
    try {
      // 模擬 API 調用
      console.log('Password changed successfully');

    } catch (error) {
      console.error('Failed to change password:', error);
      throw error;
    }
  };

  // 檢查會話是否有效
  const checkSession = async () => {
    try {
      if (!token.value) return false;

      // 簡單檢查 token 是否存在
      return !!token.value;

    } catch (error) {
      console.error('Session check failed:', error);
      return false;
    }
  };

  // 初始化用戶狀態（從持久化存儲恢復）
  const initializeUser = () => {
    // 這個方法會被 pinia-plugin-persistedstate 自動調用
    if (token.value && userInfo.value) {
      console.log(`User session restored: ${userInfo.value.name}`);
    }
  };

  // 清除敏感數據（用於安全登出）
  const clearSensitiveData = () => {
    token.value = '';
    refreshToken.value = '';
    // 保留一些基本信息用於快速重新登入
  };

  return {
    // 狀態
    token,
    refreshToken,
    userInfo,
    permissions,
    lastLoginTime,
    isLoading,

    // 計算屬性
    isLoggedIn,
    userName,
    userRole,
    userAvatar,
    isAdmin,
    isSuperAdmin,

    // 方法
    login,
    logout,
    refreshUserInfo,
    updateUserInfo,
    changePassword,
    checkSession,
    initializeUser,
    clearSensitiveData,

    // 權限方法
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  };
}, {
  persist: {
    key: 'hb-engineering-user',
    storage: localStorage,
    paths: ['token', 'refreshToken', 'userInfo', 'permissions', 'lastLoginTime'],
    beforeRestore: (context) => {
      console.log('Restoring user state from localStorage');
    },
    afterRestore: (context) => {
      const store = context.store;
      store.initializeUser();
    },
  },
});