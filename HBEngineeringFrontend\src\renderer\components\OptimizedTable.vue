<template>
  <div class="optimized-table">
    <!-- 表格工具欄 -->
    <div class="table-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <el-input
            v-model="searchQuery"
            placeholder="搜索..."
            :prefix-icon="Search"
            clearable
            style="width: 300px"
            @input="handleSearch"
          />
        </slot>
      </div>
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
          <el-dropdown v-if="showColumnSettings">
            <el-button :icon="Setting">
              列設置<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="column in columns"
                  :key="column.prop"
                  @click="toggleColumn(column)"
                >
                  <el-checkbox :model-value="!hiddenColumns.has(column.prop)">
                    {{ column.label }}
                  </el-checkbox>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </slot>
      </div>
    </div>

    <!-- 虛擬滾動表格 -->
    <div
      ref="tableContainer"
      class="table-container"
      :style="{ height: `${containerHeight}px` }"
      @scroll="handleScroll"
    >
      <div class="table-header" :style="{ transform: `translateX(-${scrollLeft}px)` }">
        <div
          v-for="column in visibleColumns"
          :key="column.prop"
          class="table-header-cell"
          :style="{ width: `${column.width || 120}px` }"
          @click="handleSort(column)"
        >
          {{ column.label }}
          <el-icon v-if="sortColumn === column.prop" class="sort-icon">
            <arrow-up v-if="sortOrder === 'asc'" />
            <arrow-down v-else />
          </el-icon>
        </div>
      </div>

      <div
        class="table-body"
        :style="{ height: `${totalHeight}px`, paddingTop: `${offsetY}px` }"
      >
        <div
          v-for="(item, index) in visibleItems"
          :key="getRowKey(item, startIndex + index)"
          class="table-row"
          :class="{ 'table-row--selected': selectedRows.has(getRowKey(item, startIndex + index)) }"
          :style="{ transform: `translateX(-${scrollLeft}px)` }"
          @click="handleRowClick(item, startIndex + index)"
        >
          <div
            v-for="column in visibleColumns"
            :key="column.prop"
            class="table-cell"
            :style="{ width: `${column.width || 120}px` }"
          >
            <slot
              :name="`cell-${column.prop}`"
              :row="item"
              :column="column"
              :index="startIndex + index"
            >
              {{ getCellValue(item, column) }}
            </slot>
          </div>
        </div>
      </div>

      <!-- 加載狀態 -->
      <div v-if="loading" class="table-loading">
        <el-icon class="is-loading"><loading /></el-icon>
        <span>加載中...</span>
      </div>

      <!-- 空狀態 -->
      <div v-else-if="!data.length" class="table-empty">
        <slot name="empty">
          <el-empty description="暫無數據" />
        </slot>
      </div>
    </div>

    <!-- 分頁 -->
    <div class="table-pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useVirtualScroll, useDebounce } from '@/composables/usePerformance';
import { Search, Refresh, Setting, ArrowUp, ArrowDown, Loading } from '@element-plus/icons-vue';

interface TableColumn {
  prop: string;
  label: string;
  width?: number;
  sortable?: boolean;
  formatter?: (row: any, column: any) => string;
}

interface Props {
  data: any[];
  columns: TableColumn[];
  loading?: boolean;
  rowKey?: string | ((row: any) => string);
  showToolbar?: boolean;
  showPagination?: boolean;
  showColumnSettings?: boolean;
  itemHeight?: number;
  containerHeight?: number;
  total?: number;
  pageSize?: number;
  pageSizes?: number[];
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  rowKey: 'id',
  showToolbar: true,
  showPagination: true,
  showColumnSettings: true,
  itemHeight: 48,
  containerHeight: 400,
  total: 0,
  pageSize: 20,
  pageSizes: () => [10, 20, 50, 100],
});

const emit = defineEmits<{
  search: [query: string];
  refresh: [];
  sort: [column: string, order: 'asc' | 'desc'];
  'row-click': [row: any, index: number];
  'page-change': [page: number];
  'size-change': [size: number];
}>();

// 響應式數據
const tableContainer = ref<HTMLElement>();
const searchQuery = ref('');
const sortColumn = ref('');
const sortOrder = ref<'asc' | 'desc'>('asc');
const currentPage = ref(1);
const scrollLeft = ref(0);
const hiddenColumns = ref(new Set<string>());
const selectedRows = ref(new Set<string>());

// 虛擬滾動
const { startIndex, endIndex, visibleItems, totalHeight, offsetY, handleScroll } = useVirtualScroll(
  computed(() => props.data),
  props.itemHeight,
  props.containerHeight
);

// 可見列
const visibleColumns = computed(() => 
  props.columns.filter(column => !hiddenColumns.value.has(column.prop))
);

// 防抖搜索
const { debouncedFn: debouncedSearch } = useDebounce((query: string) => {
  emit('search', query);
}, 300);

// 獲取行鍵
const getRowKey = (row: any, index: number): string => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row);
  }
  return row[props.rowKey] || index.toString();
};

// 獲取單元格值
const getCellValue = (row: any, column: TableColumn): string => {
  if (column.formatter) {
    return column.formatter(row, column);
  }
  return row[column.prop] || '';
};

// 事件處理
const handleSearch = () => {
  debouncedSearch(searchQuery.value);
};

const handleRefresh = () => {
  emit('refresh');
};

const handleSort = (column: TableColumn) => {
  if (!column.sortable) return;
  
  if (sortColumn.value === column.prop) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortColumn.value = column.prop;
    sortOrder.value = 'asc';
  }
  
  emit('sort', sortColumn.value, sortOrder.value);
};

const handleRowClick = (row: any, index: number) => {
  emit('row-click', row, index);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  emit('page-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const toggleColumn = (column: TableColumn) => {
  if (hiddenColumns.value.has(column.prop)) {
    hiddenColumns.value.delete(column.prop);
  } else {
    hiddenColumns.value.add(column.prop);
  }
};

// 監聽橫向滾動
const handleHorizontalScroll = () => {
  if (tableContainer.value) {
    scrollLeft.value = tableContainer.value.scrollLeft;
  }
};

onMounted(() => {
  if (tableContainer.value) {
    tableContainer.value.addEventListener('scroll', handleHorizontalScroll);
  }
});

onUnmounted(() => {
  if (tableContainer.value) {
    tableContainer.value.removeEventListener('scroll', handleHorizontalScroll);
  }
});
</script>

<style scoped lang="scss">
.optimized-table {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 16px;

    .toolbar-left,
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .table-container {
    position: relative;
    overflow: auto;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-base);

    .table-header {
      position: sticky;
      top: 0;
      z-index: 10;
      display: flex;
      background-color: var(--bg-secondary);
      border-bottom: 1px solid var(--border-light);

      .table-header-cell {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        font-weight: 600;
        border-right: 1px solid var(--border-light);
        cursor: pointer;
        user-select: none;

        &:hover {
          background-color: var(--bg-tertiary);
        }

        .sort-icon {
          margin-left: 4px;
          font-size: 12px;
        }
      }
    }

    .table-body {
      position: relative;

      .table-row {
        display: flex;
        border-bottom: 1px solid var(--border-light);
        cursor: pointer;

        &:hover {
          background-color: var(--bg-secondary);
        }

        &--selected {
          background-color: var(--primary-50);
        }

        .table-cell {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          border-right: 1px solid var(--border-light);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .table-loading,
    .table-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      color: var(--text-secondary);
    }
  }

  .table-pagination {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
  }
}
</style>
