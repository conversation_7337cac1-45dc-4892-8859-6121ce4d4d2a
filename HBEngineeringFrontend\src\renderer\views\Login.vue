<template>
  <div class="login-container">
    <div class="login-form">
      <div class="logo">
        <h2>蒼藍工程公司管理系統</h2>
      </div>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="0">
        <el-form-item prop="username">
          <el-input
            v-model="form.username"
            placeholder="請輸入用戶名"
            size="large">
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="請輸入密碼"
            size="large"
            @keyup.enter="handleLogin">
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="handleLogin">
            登入
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../stores/user';
import { ElMessage } from 'element-plus';
import { User, Lock } from '@element-plus/icons-vue';

const router = useRouter();
const userStore = useUserStore();

const formRef = ref();
const loading = ref(false);

// 表單數據
const form = reactive({
  username: '',
  password: ''
});

// 驗證規則
const rules = {
  username: [
    { required: true, message: '請輸入用戶名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' }
  ]
};

// 登入處理
const handleLogin = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    
    // 模擬登入
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 設置用戶信息
    userStore.setToken('mock-token');
    userStore.setUserInfo({
      id: 1,
      username: form.username,
      name: '管理員',
      role: 'admin'
    });
    
    ElMessage.success('登入成功');
    router.push('/');
    
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message);
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 400px;
}

.logo {
  text-align: center;
  margin-bottom: 30px;
}

.logo h2 {
  color: #304156;
  margin: 0;
  font-size: 24px;
}
</style> 