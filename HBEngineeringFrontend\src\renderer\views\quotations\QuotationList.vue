<template>
  <div class="quotation-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>報價單管理</span>
          <el-button type="primary" @click="$router.push('/quotations/create')">
            <el-icon><Plus /></el-icon>
            創建報價單
          </el-button>
        </div>
      </template>

      <!-- 搜索和篩選 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchForm.search"
              placeholder="搜索報價單號或客戶名稱"
              clearable
              @keyup.enter="handleSearch">
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="狀態篩選" clearable>
              <el-option label="全部" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="已發送" value="sent" />
              <el-option label="已接受" value="accepted" />
              <el-option label="已拒絕" value="rejected" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="開始日期"
              end-placeholder="結束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 數據表格 -->
      <el-table
        :data="quotations"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="quotation_no" label="報價單號" width="150" />
        <el-table-column prop="customer_name" label="客戶" width="200" />
        <el-table-column prop="created_at" label="創建日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="valid_until" label="有效期至" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.valid_until) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="總金額" width="120" align="right">
          <template #default="scope">
            ${{ scope.row.total_amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="discount_amount" label="折扣" width="100" align="right">
          <template #default="scope">
            ${{ scope.row.discount_amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="final_amount" label="最終金額" width="120" align="right">
          <template #default="scope">
            <span style="color: #67C23A; font-weight: bold;">
              ${{ scope.row.final_amount.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="狀態" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleEdit(scope.row)">
              編輯
            </el-button>
            <el-dropdown @command="(command) => handleDropdownCommand(command, scope.row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="pdf">
                    <el-icon><Document /></el-icon>
                    下載PDF
                  </el-dropdown-item>
                  <el-dropdown-item command="invoice">
                    <el-icon><Tickets /></el-icon>
                    轉為發票
                  </el-dropdown-item>
                  <el-dropdown-item command="delivery">
                    <el-icon><Box /></el-icon>
                    轉為送貨單
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    刪除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search, Refresh, Document, Tickets, Box, Delete, ArrowDown } from '@element-plus/icons-vue';
import dayjs from 'dayjs';

const router = useRouter();

// 數據狀態
const quotations = ref([]);
const loading = ref(false);
const selectedRows = ref([]);

// 分頁狀態
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索表單
const searchForm = reactive({
  search: '',
  status: '',
  dateRange: []
});

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD');
};

// 狀態類型映射
const getStatusType = (status) => {
  const statusMap = {
    'draft': 'info',
    'sent': 'warning',
    'accepted': 'success',
    'rejected': 'danger'
  };
  return statusMap[status] || 'info';
};

// 狀態文本映射
const getStatusText = (status) => {
  const statusMap = {
    'draft': '草稿',
    'sent': '已發送',
    'accepted': '已接受',
    'rejected': '已拒絕'
  };
  return statusMap[status] || status;
};

// 獲取報價單列表
const fetchQuotations = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      search: searchForm.search,
      status: searchForm.status
    };
    
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0];
      params.end_date = searchForm.dateRange[1];
    }

    // 模擬API調用
    const mockData = {
      quotations: [
        {
          id: 1,
          quotation_no: 'QT-202401-0001',
          customer_name: 'ABC電子有限公司',
          created_at: '2024-01-15',
          valid_until: '2024-02-15',
          total_amount: 15000.00,
          discount_amount: 750.00,
          final_amount: 14250.00,
          status: 'accepted'
        },
        {
          id: 2,
          quotation_no: 'QT-202401-0002',
          customer_name: 'XYZ工程公司',
          created_at: '2024-01-16',
          valid_until: '2024-02-16',
          total_amount: 8500.00,
          discount_amount: 0.00,
          final_amount: 8500.00,
          status: 'sent'
        },
        {
          id: 3,
          quotation_no: 'QT-202401-0003',
          customer_name: '香港電器有限公司',
          created_at: '2024-01-17',
          valid_until: '2024-02-17',
          total_amount: 12300.00,
          discount_amount: 615.00,
          final_amount: 11685.00,
          status: 'draft'
        }
      ],
      total: 3,
      page: 1,
      per_page: 10
    };

    quotations.value = mockData.quotations;
    total.value = mockData.total;
    
  } catch (error) {
    ElMessage.error('獲取報價單列表失敗');
  } finally {
    loading.value = false;
  }
};

// 搜索處理
const handleSearch = () => {
  currentPage.value = 1;
  fetchQuotations();
};

// 重置搜索
const handleReset = () => {
  searchForm.search = '';
  searchForm.status = '';
  searchForm.dateRange = [];
  currentPage.value = 1;
  fetchQuotations();
};

// 選擇變更處理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 分頁處理
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchQuotations();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchQuotations();
};

// 查看報價單
const handleView = (row) => {
  router.push(`/quotations/${row.id}`);
};

// 編輯報價單
const handleEdit = (row) => {
  router.push(`/quotations/${row.id}/edit`);
};

// 下拉菜單命令處理
const handleDropdownCommand = async (command, row) => {
  switch (command) {
    case 'pdf':
      await handleDownloadPDF(row);
      break;
    case 'invoice':
      await handleConvertToInvoice(row);
      break;
    case 'delivery':
      await handleConvertToDeliveryNote(row);
      break;
    case 'delete':
      await handleDelete(row);
      break;
  }
};

// 下載PDF
const handleDownloadPDF = async (row) => {
  try {
    // 模擬PDF下載
    ElMessage.success(`正在下載 ${row.quotation_no} 的PDF文件`);
  } catch (error) {
    ElMessage.error('下載PDF失敗');
  }
};

// 轉為發票
const handleConvertToInvoice = async (row) => {
  try {
    await ElMessageBox.confirm(
      `確定要將報價單 ${row.quotation_no} 轉換為發票嗎？`,
      '確認轉換',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 模擬轉換
    ElMessage.success('轉換成功');
    router.push('/invoices');
  } catch {
    // 用戶取消
  }
};

// 轉為送貨單
const handleConvertToDeliveryNote = async (row) => {
  try {
    await ElMessageBox.confirm(
      `確定要將報價單 ${row.quotation_no} 轉換為送貨單嗎？`,
      '確認轉換',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 模擬轉換
    ElMessage.success('轉換成功');
    router.push('/delivery-notes');
  } catch {
    // 用戶取消
  }
};

// 刪除報價單
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除報價單 ${row.quotation_no} 嗎？此操作不可恢復。`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 模擬刪除
    ElMessage.success('刪除成功');
    fetchQuotations();
  } catch {
    // 用戶取消
  }
};

onMounted(() => {
  fetchQuotations();
});
</script>

<style scoped>
.quotation-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 