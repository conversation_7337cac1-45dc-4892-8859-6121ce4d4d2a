import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useProductStore = defineStore('product', () => {
  // 狀態
  const products = ref([]);
  const currentProduct = ref(null);
  const loading = ref(false);
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0
  });
  
  // 搜索和篩選
  const searchQuery = ref('');
  const categoryFilter = ref('');
  const statusFilter = ref('');
  const lowStockOnly = ref(false);

  // 產品分類
  const categories = ref([
    { id: '1', name: '建築材料', code: 'MATERIAL' },
    { id: '2', name: '工程設備', code: 'EQUIPMENT' },
    { id: '3', name: '安全用品', code: 'SAFETY' },
    { id: '4', name: '工具器材', code: 'TOOLS' },
    { id: '5', name: '電器設備', code: 'ELECTRICAL' },
    { id: '6', name: '五金配件', code: 'HARDWARE' }
  ]);

  // 計算屬性
  const filteredProducts = computed(() => {
    let result = products.value;
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(p => 
        p.name.toLowerCase().includes(query) ||
        p.code.toLowerCase().includes(query) ||
        p.description.toLowerCase().includes(query)
      );
    }
    
    if (categoryFilter.value) {
      result = result.filter(p => p.categoryId === categoryFilter.value);
    }
    
    if (statusFilter.value) {
      result = result.filter(p => p.status === statusFilter.value);
    }
    
    if (lowStockOnly.value) {
      result = result.filter(p => p.stockQuantity <= p.minStockLevel);
    }
    
    return result;
  });

  const totalPages = computed(() => {
    return Math.ceil(filteredProducts.value.length / pagination.value.pageSize);
  });

  const paginatedProducts = computed(() => {
    const start = (pagination.value.page - 1) * pagination.value.pageSize;
    const end = start + pagination.value.pageSize;
    return filteredProducts.value.slice(start, end);
  });

  // 統計數據
  const statistics = computed(() => {
    const total = products.value.length;
    const active = products.value.filter(p => p.status === 'active').length;
    const inactive = products.value.filter(p => p.status === 'inactive').length;
    const lowStock = products.value.filter(p => p.stockQuantity <= p.minStockLevel).length;
    const outOfStock = products.value.filter(p => p.stockQuantity === 0).length;
    
    const totalValue = products.value.reduce((sum, p) => 
      sum + (p.stockQuantity * p.unitPrice), 0);
    
    return {
      total,
      active,
      inactive,
      lowStock,
      outOfStock,
      totalValue
    };
  });

  // 低庫存產品
  const lowStockProducts = computed(() => {
    return products.value.filter(p => p.stockQuantity <= p.minStockLevel);
  });

  // Actions
  const fetchProducts = async (params = {}) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockData = generateMockProducts(50);
      products.value = mockData;
      pagination.value.total = mockData.length;
      
      return { success: true, data: mockData };
    } catch (error) {
      console.error('Failed to fetch products:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const getProduct = async (id) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const product = products.value.find(p => p.id === id) || 
        generateMockProduct(id);
      
      currentProduct.value = product;
      return { success: true, data: product };
    } catch (error) {
      console.error('Failed to fetch product:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const createProduct = async (productData) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newProduct = {
        ...productData,
        id: Date.now().toString(),
        code: generateProductCode(),
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      products.value.unshift(newProduct);
      pagination.value.total++;
      
      return { success: true, data: newProduct };
    } catch (error) {
      console.error('Failed to create product:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const updateProduct = async (id, productData) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = products.value.findIndex(p => p.id === id);
      if (index !== -1) {
        products.value[index] = {
          ...products.value[index],
          ...productData,
          updatedAt: new Date().toISOString()
        };
        
        if (currentProduct.value?.id === id) {
          currentProduct.value = products.value[index];
        }
      }
      
      return { success: true, data: products.value[index] };
    } catch (error) {
      console.error('Failed to update product:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const deleteProduct = async (id) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = products.value.findIndex(p => p.id === id);
      if (index !== -1) {
        products.value.splice(index, 1);
        pagination.value.total--;
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to delete product:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const updateStatus = async (id, status) => {
    return updateProduct(id, { status });
  };

  const updateStock = async (id, quantity, type = 'set') => {
    const product = products.value.find(p => p.id === id);
    if (!product) return { success: false, error: 'Product not found' };
    
    let newQuantity;
    switch (type) {
      case 'add':
        newQuantity = product.stockQuantity + quantity;
        break;
      case 'subtract':
        newQuantity = Math.max(0, product.stockQuantity - quantity);
        break;
      default:
        newQuantity = quantity;
    }
    
    return updateProduct(id, { stockQuantity: newQuantity });
  };

  const getCategoryById = (id) => {
    return categories.value.find(c => c.id === id);
  };

  // 重置狀態
  const reset = () => {
    products.value = [];
    currentProduct.value = null;
    loading.value = false;
    pagination.value = {
      page: 1,
      pageSize: 10,
      total: 0
    };
    searchQuery.value = '';
    categoryFilter.value = '';
    statusFilter.value = '';
    lowStockOnly.value = false;
  };

  return {
    // 狀態
    products: paginatedProducts,
    allProducts: products,
    currentProduct,
    loading,
    pagination,
    searchQuery,
    categoryFilter,
    statusFilter,
    lowStockOnly,
    categories,
    
    // 計算屬性
    filteredProducts,
    totalPages,
    statistics,
    lowStockProducts,
    
    // Actions
    fetchProducts,
    getProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    updateStatus,
    updateStock,
    getCategoryById,
    reset
  };
});

// 生成產品編號
function generateProductCode() {
  const year = new Date().getFullYear();
  const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `PRD${year}${random}`;
}

// 生成模擬產品數據
function generateMockProduct(id = null) {
  const categories = ['1', '2', '3', '4', '5', '6'];
  const statuses = ['active', 'inactive'];
  const units = ['個', '套', '米', '公斤', '箱', '包'];
  
  const productNames = [
    '水泥', '鋼筋', '混凝土', '磚塊', '瓷磚', '油漆',
    '電線', '開關', '插座', '燈具', '管道', '閥門',
    '安全帽', '安全鞋', '手套', '護目鏡', '反光衣',
    '電鑽', '切割機', '測量儀', '起重機', '壓路機'
  ];
  
  const name = productNames[Math.floor(Math.random() * productNames.length)];
  const stockQuantity = Math.floor(Math.random() * 1000);
  const minStockLevel = Math.floor(Math.random() * 50) + 10;
  
  return {
    id: id || Date.now().toString(),
    code: generateProductCode(),
    name: `${name} ${Math.floor(Math.random() * 100) + 1}型`,
    description: `優質${name}，適用於各種工程項目`,
    categoryId: categories[Math.floor(Math.random() * categories.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    unitPrice: Math.floor(Math.random() * 10000) + 100,
    stockQuantity,
    minStockLevel,
    unit: units[Math.floor(Math.random() * units.length)],
    supplier: `供應商${Math.floor(Math.random() * 10) + 1}`,
    barcode: `${Math.floor(Math.random() * 9000000000000) + 1000000000000}`,
    weight: Math.floor(Math.random() * 100) + 1,
    dimensions: `${Math.floor(Math.random() * 100) + 10}x${Math.floor(Math.random() * 100) + 10}x${Math.floor(Math.random() * 100) + 10}`,
    notes: '產品備註信息',
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  };
}

function generateMockProducts(count) {
  return Array.from({ length: count }, () => generateMockProduct());
} 