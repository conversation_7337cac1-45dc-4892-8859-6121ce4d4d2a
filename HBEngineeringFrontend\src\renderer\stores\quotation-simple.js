import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { quotationAPI } from '../../api/simple-client.js';
import { ElMessage } from 'element-plus';

export const useQuotationStore = defineStore('quotation', () => {
  // 狀態
  const quotations = ref([]);
  const currentQuotation = ref(null);
  const loading = ref(false);
  const submitting = ref(false);
  const error = ref(null);
  
  const filters = ref({
    search: '',
    status: '',
    customer_id: '',
    date_from: '',
    date_to: '',
    amount_min: '',
    amount_max: '',
  });
  
  const pagination = ref({
    page: 1,
    size: 20,
    total: 0,
  });

  // 計算屬性
  const filteredQuotations = computed(() => {
    let result = quotations.value;
    
    // 搜索過濾
    if (filters.value.search) {
      const query = filters.value.search.toLowerCase();
      result = result.filter(q => 
        q.quotation_no?.toLowerCase().includes(query) ||
        q.customer_name?.toLowerCase().includes(query) ||
        q.project_name?.toLowerCase().includes(query) ||
        q.description?.toLowerCase().includes(query)
      );
    }
    
    // 狀態過濾
    if (filters.value.status) {
      result = result.filter(q => q.status === filters.value.status);
    }
    
    // 客戶過濾
    if (filters.value.customer_id) {
      result = result.filter(q => q.customer_id?.toString() === filters.value.customer_id);
    }
    
    // 日期範圍過濾
    if (filters.value.date_from) {
      result = result.filter(q => new Date(q.created_at) >= new Date(filters.value.date_from));
    }
    if (filters.value.date_to) {
      result = result.filter(q => new Date(q.created_at) <= new Date(filters.value.date_to));
    }
    
    // 金額範圍過濾
    if (filters.value.amount_min) {
      result = result.filter(q => q.final_amount >= parseFloat(filters.value.amount_min));
    }
    if (filters.value.amount_max) {
      result = result.filter(q => q.final_amount <= parseFloat(filters.value.amount_max));
    }
    
    return result;
  });

  const totalPages = computed(() => {
    return Math.ceil(pagination.value.total / pagination.value.size);
  });

  // 統計數據
  const statistics = computed(() => {
    const all = quotations.value;
    const thisMonth = all.filter(q => {
      const itemDate = new Date(q.created_at);
      const now = new Date();
      return itemDate.getMonth() === now.getMonth() && itemDate.getFullYear() === now.getFullYear();
    });
    
    return {
      total: all.length,
      thisMonth: thisMonth.length,
      draft: all.filter(q => q.status === 'draft').length,
      sent: all.filter(q => q.status === 'sent').length,
      accepted: all.filter(q => q.status === 'accepted').length,
      rejected: all.filter(q => q.status === 'rejected').length,
      expired: all.filter(q => q.status === 'expired').length,
      totalAmount: all.reduce((sum, q) => sum + (q.final_amount || 0), 0),
      acceptedAmount: all.filter(q => q.status === 'accepted').reduce((sum, q) => sum + (q.final_amount || 0), 0),
      thisMonthAmount: thisMonth.reduce((sum, q) => sum + (q.final_amount || 0), 0),
      averageAmount: all.length > 0 ? all.reduce((sum, q) => sum + (q.final_amount || 0), 0) / all.length : 0,
    };
  });

  // 狀態選項
  const statusOptions = computed(() => [
    { label: '草稿', value: 'draft', color: '#909399' },
    { label: '已發送', value: 'sent', color: '#E6A23C' },
    { label: '已接受', value: 'accepted', color: '#67C23A' },
    { label: '已拒絕', value: 'rejected', color: '#F56C6C' },
    { label: '已過期', value: 'expired', color: '#C0C4CC' },
  ]);

  // 獲取狀態標籤
  const getStatusLabel = (status) => {
    const option = statusOptions.value.find(opt => opt.value === status);
    return option?.label || status;
  };

  // 獲取狀態顏色
  const getStatusColor = (status) => {
    const option = statusOptions.value.find(opt => opt.value === status);
    return option?.color || '#909399';
  };

  // Actions
  const fetchQuotations = async (params = {}) => {
    try {
      loading.value = true;
      error.value = null;
      
      const requestParams = {
        page: pagination.value.page,
        size: pagination.value.size,
        ...filters.value,
        ...params,
      };
      
      const response = await quotationAPI.getList(requestParams);
      quotations.value = response.data?.items || response.data || [];
      pagination.value.total = response.data?.total || response.total || 0;
      
    } catch (err) {
      error.value = err.message || '獲取報價單列表失敗';
      ElMessage.error(error.value);
      quotations.value = [];
      pagination.value.total = 0;
    } finally {
      loading.value = false;
    }
  };

  const getQuotation = async (id) => {
    try {
      loading.value = true;
      error.value = null;
      
      const response = await quotationAPI.getById(id);
      currentQuotation.value = response.data;
      return response.data;
      
    } catch (err) {
      error.value = err.message || '獲取報價單詳情失敗';
      ElMessage.error(error.value);
      currentQuotation.value = null;
      return null;
    } finally {
      loading.value = false;
    }
  };

  const createQuotation = async (quotationData) => {
    try {
      submitting.value = true;
      error.value = null;
      
      const response = await quotationAPI.create(quotationData);
      const newQuotation = response.data;
      
      // 添加到列表頂部
      quotations.value.unshift(newQuotation);
      pagination.value.total++;
      
      ElMessage.success('報價單創建成功');
      return newQuotation;
      
    } catch (err) {
      error.value = err.message || '創建報價單失敗';
      ElMessage.error(error.value);
      return null;
    } finally {
      submitting.value = false;
    }
  };

  const updateQuotation = async (id, quotationData) => {
    try {
      submitting.value = true;
      error.value = null;
      
      const response = await quotationAPI.update(id, quotationData);
      const updatedQuotation = response.data;
      
      // 更新列表中的項目
      const index = quotations.value.findIndex(q => q.id === id);
      if (index !== -1) {
        quotations.value[index] = updatedQuotation;
      }
      
      // 更新當前項目
      if (currentQuotation.value?.id === id) {
        currentQuotation.value = updatedQuotation;
      }
      
      ElMessage.success('報價單更新成功');
      return updatedQuotation;
      
    } catch (err) {
      error.value = err.message || '更新報價單失敗';
      ElMessage.error(error.value);
      return null;
    } finally {
      submitting.value = false;
    }
  };

  const deleteQuotation = async (id) => {
    try {
      loading.value = true;
      error.value = null;
      
      await quotationAPI.delete(id);
      
      // 從列表中移除
      const index = quotations.value.findIndex(q => q.id === id);
      if (index !== -1) {
        quotations.value.splice(index, 1);
        pagination.value.total--;
      }
      
      // 清除當前項目
      if (currentQuotation.value?.id === id) {
        currentQuotation.value = null;
      }
      
      ElMessage.success('報價單刪除成功');
      return true;
      
    } catch (err) {
      error.value = err.message || '刪除報價單失敗';
      ElMessage.error(error.value);
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 設置篩選條件
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters };
    pagination.value.page = 1; // 重置到第一頁
  };

  // 重置篩選條件
  const resetFilters = () => {
    filters.value = {
      search: '',
      status: '',
      customer_id: '',
      date_from: '',
      date_to: '',
      amount_min: '',
      amount_max: '',
    };
    pagination.value.page = 1;
  };

  // 設置分頁
  const setPagination = (page, size) => {
    pagination.value.page = page;
    if (size) {
      pagination.value.size = size;
    }
  };

  // 重置狀態
  const reset = () => {
    quotations.value = [];
    currentQuotation.value = null;
    loading.value = false;
    submitting.value = false;
    error.value = null;
    pagination.value = {
      page: 1,
      size: 20,
      total: 0,
    };
    resetFilters();
  };

  return {
    // 狀態
    quotations,
    currentQuotation,
    loading,
    submitting,
    error,
    filters,
    pagination,
    
    // 計算屬性
    filteredQuotations,
    totalPages,
    statistics,
    statusOptions,
    getStatusLabel,
    getStatusColor,
    
    // Actions
    fetchQuotations,
    getQuotation,
    createQuotation,
    updateQuotation,
    deleteQuotation,
    setFilters,
    resetFilters,
    setPagination,
    reset,
  };
}, {
  persist: {
    key: 'hb-engineering-quotation',
    storage: sessionStorage,
    paths: ['filters', 'pagination'],
  },
});
