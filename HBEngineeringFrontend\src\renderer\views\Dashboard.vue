<template>
  <div class="dashboard">
    <!-- 統計卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon" style="background-color: #409EFF;">
              <el-icon size="24"><Document /></el-icon>
            </div>
            <div class="stats-text">
              <h3>{{ stats.quotations }}</h3>
              <p>報價單總數</p>
              <span class="stats-change">+{{ stats.quotationsChange }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon" style="background-color: #67C23A;">
              <el-icon size="24"><Tickets /></el-icon>
            </div>
            <div class="stats-text">
              <h3>{{ stats.invoices }}</h3>
              <p>發票總數</p>
              <span class="stats-change">+{{ stats.invoicesChange }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon" style="background-color: #E6A23C;">
              <el-icon size="24"><Box /></el-icon>
            </div>
            <div class="stats-text">
              <h3>{{ stats.deliveryNotes }}</h3>
              <p>送貨單總數</p>
              <span class="stats-change">+{{ stats.deliveryNotesChange }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon" style="background-color: #F56C6C;">
              <el-icon size="24"><User /></el-icon>
            </div>
            <div class="stats-text">
              <h3>{{ stats.customers }}</h3>
              <p>客戶總數</p>
              <span class="stats-change">+{{ stats.customersChange }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 圖表和列表 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近報價單</span>
              <el-button type="primary" size="small" @click="$router.push('/quotations')">
                查看全部
              </el-button>
            </div>
          </template>
          <el-table :data="recentQuotations" style="width: 100%">
            <el-table-column prop="quotation_no" label="報價單號" width="150" />
            <el-table-column prop="customer_name" label="客戶" />
            <el-table-column prop="final_amount" label="金額" width="120">
              <template #default="scope">
                <span style="color: #67C23A; font-weight: bold;">
                  ${{ scope.row.final_amount.toFixed(2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="狀態" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近發票</span>
              <el-button type="primary" size="small" @click="$router.push('/invoices')">
                查看全部
              </el-button>
            </div>
          </template>
          <el-table :data="recentInvoices" style="width: 100%">
            <el-table-column prop="invoice_no" label="發票號" width="150" />
            <el-table-column prop="customer_name" label="客戶" />
            <el-table-column prop="final_amount" label="金額" width="120">
              <template #default="scope">
                <span style="color: #67C23A; font-weight: bold;">
                  ${{ scope.row.final_amount.toFixed(2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="狀態" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 月度統計圖表 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>月度業務統計</span>
              <el-radio-group v-model="chartType" size="small">
                <el-radio-button label="line">線圖</el-radio-button>
                <el-radio-button label="bar">柱狀圖</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <canvas ref="chartCanvas" width="600" height="300"></canvas>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>業務狀態分佈</span>
            </div>
          </template>
          <div class="status-stats">
            <div class="status-item" v-for="item in statusStats" :key="item.label">
              <div class="status-dot" :style="{ backgroundColor: item.color }"></div>
              <span class="status-label">{{ item.label }}</span>
              <span class="status-count">{{ item.count }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 待辦事項和通知 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>待辦事項</span>
              <el-badge :value="todoItems.length" type="primary">
                <el-icon><Bell /></el-icon>
              </el-badge>
            </div>
          </template>
          <div class="todo-list">
            <div class="todo-item" v-for="item in todoItems" :key="item.id">
              <el-icon :color="item.priority === 'high' ? '#F56C6C' : '#E6A23C'">
                <Warning v-if="item.priority === 'high'" />
                <InfoFilled v-else />
              </el-icon>
              <div class="todo-content">
                <p class="todo-title">{{ item.title }}</p>
                <p class="todo-desc">{{ item.description }}</p>
                <span class="todo-date">{{ item.date }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>庫存警告</span>
              <el-badge :value="lowStockItems.length" type="danger">
                <el-icon><Box /></el-icon>
              </el-badge>
            </div>
          </template>
          <div class="stock-alerts">
            <div class="stock-item" v-for="item in lowStockItems" :key="item.id">
              <el-icon color="#F56C6C"><Warning /></el-icon>
              <div class="stock-content">
                <p class="stock-name">{{ item.name }}</p>
                <p class="stock-level">
                  剩餘: <span class="low-stock">{{ item.stock }}</span> / 
                  最低: {{ item.minStock }}
                </p>
              </div>
              <el-button size="small" type="danger" plain @click="restockItem(item)">
                補貨
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Document, Tickets, Box, User, Bell, Warning, InfoFilled } from '@element-plus/icons-vue';

// 統計數據
const stats = ref({
  quotations: 0,
  quotationsChange: 0,
  invoices: 0,
  invoicesChange: 0,
  deliveryNotes: 0,
  deliveryNotesChange: 0,
  customers: 0,
  customersChange: 0
});

// 最近數據
const recentQuotations = ref([]);
const recentInvoices = ref([]);

// 圖表引用和類型
const chartCanvas = ref(null);
const chartType = ref('line');

// 狀態統計
const statusStats = ref([]);

// 待辦事項
const todoItems = ref([]);

// 庫存警告
const lowStockItems = ref([]);

// 狀態類型映射
const getStatusType = (status) => {
  const statusMap = {
    'draft': 'info',
    'sent': 'warning',
    'accepted': 'success',
    'rejected': 'danger',
    'completed': 'success',
    'cancelled': 'danger'
  };
  return statusMap[status] || 'info';
};

// 狀態文本映射
const getStatusText = (status) => {
  const statusMap = {
    'draft': '草稿',
    'sent': '已發送',
    'accepted': '已接受',
    'rejected': '已拒絕',
    'completed': '已完成',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// 獲取統計數據
const fetchStats = async () => {
  try {
    // 模擬數據
    stats.value = {
      quotations: 125,
      quotationsChange: 15,
      invoices: 98,
      invoicesChange: 12,
      deliveryNotes: 87,
      deliveryNotesChange: 10,
      customers: 45,
      customersChange: 8
    };
  } catch (error) {
    console.error('獲取統計數據失敗:', error);
  }
};

// 獲取最近數據
const fetchRecentData = async () => {
  try {
    // 模擬數據
    recentQuotations.value = [
      {
        quotation_no: 'QT-202401-0001',
        customer_name: 'ABC電子有限公司',
        final_amount: 15000.00,
        status: 'accepted'
      },
      {
        quotation_no: 'QT-202401-0002',
        customer_name: 'XYZ工程公司',
        final_amount: 8500.00,
        status: 'sent'
      },
      {
        quotation_no: 'QT-202401-0003',
        customer_name: '香港電器有限公司',
        final_amount: 12300.00,
        status: 'draft'
      }
    ];

    recentInvoices.value = [
      {
        invoice_no: 'INV-202401-0001',
        customer_name: 'ABC電子有限公司',
        final_amount: 15000.00,
        status: 'completed'
      },
      {
        invoice_no: 'INV-202401-0002',
        customer_name: 'XYZ工程公司',
        final_amount: 8500.00,
        status: 'sent'
      },
      {
        invoice_no: 'INV-202401-0003',
        customer_name: '香港電器有限公司',
        final_amount: 12300.00,
        status: 'completed'
      }
    ];
  } catch (error) {
    console.error('獲取最近數據失敗:', error);
  }
};

// 獲取狀態統計
const fetchStatusStats = async () => {
  statusStats.value = [
    { label: '已完成', count: 45, color: '#67C23A' },
    { label: '進行中', count: 23, color: '#E6A23C' },
    { label: '待處理', count: 12, color: '#409EFF' },
    { label: '已取消', count: 5, color: '#F56C6C' }
  ];
};

// 獲取待辦事項
const fetchTodoItems = async () => {
  todoItems.value = [
    {
      id: 1,
      title: '跟進 ABC 公司報價',
      description: '需要在明天前回覆客戶詢問',
      date: '2024-01-15',
      priority: 'high'
    },
    {
      id: 2,
      title: '更新產品價格',
      description: '季度價格調整，需要更新系統',
      date: '2024-01-18',
      priority: 'medium'
    },
    {
      id: 3,
      title: '準備月度報告',
      description: '整理本月業務數據',
      date: '2024-01-20',
      priority: 'medium'
    }
  ];
};

// 獲取庫存警告
const fetchLowStockItems = async () => {
  lowStockItems.value = [
    {
      id: 1,
      name: '電阻器 1KΩ',
      stock: 5,
      minStock: 20
    },
    {
      id: 2,
      name: '電容器 10μF',
      stock: 8,
      minStock: 25
    },
    {
      id: 3,
      name: 'LED 燈泡',
      stock: 3,
      minStock: 15
    }
  ];
};

// 補貨功能
const restockItem = async (item) => {
  try {
    const { value } = await ElMessageBox.prompt(
      `請輸入 ${item.name} 的補貨數量:`,
      '補貨',
      {
        confirmButtonText: '確認補貨',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '請輸入有效的數量'
      }
    );
    
    const quantity = parseInt(value);
    item.stock += quantity;
    
    ElMessage.success(`${item.name} 已補貨 ${quantity} 件`);
    
    // 如果補貨後高於最低庫存，從警告列表中移除
    if (item.stock >= item.minStock) {
      const index = lowStockItems.value.findIndex(i => i.id === item.id);
      if (index > -1) {
        lowStockItems.value.splice(index, 1);
      }
    }
  } catch (error) {
    // 用戶取消或輸入無效
  }
};

// 繪製圖表
const drawChart = () => {
  const canvas = chartCanvas.value;
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  const width = canvas.width;
  const height = canvas.height;
  
  // 清空畫布
  ctx.clearRect(0, 0, width, height);
  
  // 模擬月度數據
  const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
  const quotationData = [20, 25, 30, 35, 28, 40];
  const invoiceData = [15, 20, 25, 30, 22, 35];
  
  // 設置圖表參數
  const padding = 50;
  const chartWidth = width - 2 * padding;
  const chartHeight = height - 2 * padding;
  const maxValue = Math.max(...quotationData, ...invoiceData);
  
  // 繪製網格線
  ctx.strokeStyle = '#f0f0f0';
  ctx.lineWidth = 1;
  
  // 垂直網格線
  for (let i = 0; i <= months.length; i++) {
    const x = padding + (i * chartWidth) / months.length;
    ctx.beginPath();
    ctx.moveTo(x, padding);
    ctx.lineTo(x, height - padding);
    ctx.stroke();
  }
  
  // 水平網格線
  for (let i = 0; i <= 5; i++) {
    const y = padding + (i * chartHeight) / 5;
    ctx.beginPath();
    ctx.moveTo(padding, y);
    ctx.lineTo(width - padding, y);
    ctx.stroke();
  }
  
  if (chartType.value === 'line') {
    // 繪製線圖
    // 繪製報價單數據線
    ctx.strokeStyle = '#409EFF';
    ctx.lineWidth = 3;
    ctx.beginPath();
    quotationData.forEach((value, index) => {
      const x = padding + ((index + 0.5) * chartWidth) / months.length;
      const y = height - padding - (value / maxValue) * chartHeight;
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.stroke();
    
    // 繪製發票數據線
    ctx.strokeStyle = '#67C23A';
    ctx.lineWidth = 3;
    ctx.beginPath();
    invoiceData.forEach((value, index) => {
      const x = padding + ((index + 0.5) * chartWidth) / months.length;
      const y = height - padding - (value / maxValue) * chartHeight;
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.stroke();
  } else {
    // 繪製柱狀圖
    const barWidth = chartWidth / months.length / 3;
    
    quotationData.forEach((value, index) => {
      const x = padding + (index * chartWidth) / months.length + barWidth / 2;
      const barHeight = (value / maxValue) * chartHeight;
      const y = height - padding - barHeight;
      
      ctx.fillStyle = '#409EFF';
      ctx.fillRect(x, y, barWidth, barHeight);
    });
    
    invoiceData.forEach((value, index) => {
      const x = padding + (index * chartWidth) / months.length + barWidth * 1.5;
      const barHeight = (value / maxValue) * chartHeight;
      const y = height - padding - barHeight;
      
      ctx.fillStyle = '#67C23A';
      ctx.fillRect(x, y, barWidth, barHeight);
    });
  }
  
  // 繪製X軸標籤
  ctx.fillStyle = '#606266';
  ctx.font = '12px Arial';
  ctx.textAlign = 'center';
  months.forEach((month, index) => {
    const x = padding + ((index + 0.5) * chartWidth) / months.length;
    ctx.fillText(month, x, height - padding + 20);
  });
  
  // 繪製圖例
  ctx.fillStyle = '#409EFF';
  ctx.fillRect(padding, 20, 20, 10);
  ctx.fillStyle = '#606266';
  ctx.font = '12px Arial';
  ctx.textAlign = 'left';
  ctx.fillText('報價單', padding + 30, 30);
  
  ctx.fillStyle = '#67C23A';
  ctx.fillRect(padding + 100, 20, 20, 10);
  ctx.fillText('發票', padding + 130, 30);
};

// 監聽圖表類型變化
watch(chartType, () => {
  setTimeout(() => {
    drawChart();
  }, 100);
});

onMounted(() => {
  fetchStats();
  fetchRecentData();
  fetchStatusStats();
  fetchTodoItems();
  fetchLowStockItems();
  setTimeout(() => {
    drawChart();
  }, 100);
});
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 15px;
}

.stats-text {
  flex: 1;
}

.stats-text h3 {
  margin: 0 0 5px 0;
  font-size: 28px;
  color: #303133;
}

.stats-text p {
  margin: 0 0 5px 0;
  color: #606266;
  font-size: 14px;
}

.stats-change {
  color: #67C23A;
  font-size: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  width: 100%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-stats {
  padding: 10px 0;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}

.status-label {
  flex: 1;
  color: #606266;
}

.status-count {
  font-weight: bold;
  color: #303133;
}

.todo-list {
  max-height: 280px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-content {
  flex: 1;
  margin-left: 10px;
}

.todo-title {
  margin: 0 0 5px 0;
  font-weight: bold;
  color: #303133;
}

.todo-desc {
  margin: 0 0 5px 0;
  color: #606266;
  font-size: 12px;
}

.todo-date {
  color: #909399;
  font-size: 11px;
}

.stock-alerts {
  max-height: 280px;
  overflow-y: auto;
}

.stock-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stock-item:last-child {
  border-bottom: none;
}

.stock-content {
  flex: 1;
  margin-left: 10px;
}

.stock-name {
  margin: 0 0 5px 0;
  font-weight: bold;
  color: #303133;
}

.stock-level {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

.low-stock {
  color: #F56C6C;
  font-weight: bold;
}
</style> 