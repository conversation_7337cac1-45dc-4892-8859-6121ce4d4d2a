<template>
  <el-config-provider :locale="locale">
    <router-view></router-view>
  </el-config-provider>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import zhHK from 'element-plus/dist/locale/zh-hk.mjs';

const { locale: i18nLocale } = useI18n();
const locale = computed(() => {
  return i18nLocale.value === 'zh-HK' ? zhHK : null;
});
</script>

<style>
#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}
</style> 