const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 5000;

// 啟用 CORS 和 JSON 解析
app.use(cors());
app.use(express.json());

// 模擬數據
const mockData = {
  users: [{ id: 1, username: 'admin', email: '<EMAIL>', role: 'admin' }],
  customers: [
    { id: 1, name: '示例公司', contact_person: '張先生', phone: '2123-4567', email: '<EMAIL>' },
    { id: 2, name: 'ABC工程有限公司', contact_person: '李小姐', phone: '2765-4321', email: '<EMAIL>' }
  ],
  products: [
    { id: 1, name: 'LED燈具', description: '高效能LED照明設備', unit_price: 150.00, unit: 'pc' },
    { id: 2, name: '電力線纜', description: '工業級電力傳輸線纜', unit_price: 85.50, unit: 'm' }
  ],
  quotations: [
    { 
      id: 1, 
      quotation_no: 'QT-202412-001', 
      customer_name: '示例公司', 
      total_amount: 5000.00, 
      status: 'pending',
      created_at: '2024-12-01'
    }
  ],
  invoices: [],
  delivery_notes: []
};

// 認證相關 API
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  if (username === 'admin' && password === 'admin123') {
    res.json({
      success: true,
      access_token: 'mock-jwt-token-12345',
      user: mockData.users[0]
    });
  } else {
    res.status(401).json({ success: false, error: '用戶名或密碼錯誤' });
  }
});

// 系統資訊 API
app.get('/api/system/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    database: 'connected'
  });
});

app.get('/api/system/info', (req, res) => {
  res.json({
    success: true,
    system: 'H.B Engineering Management System (模擬版)',
    version: '2.0.0-mock',
    backend: 'Node.js Express (Mock)',
    database: 'Memory',
    platform: 'Windows'
  });
});

// 客戶管理 API
app.get('/api/customers', (req, res) => {
  res.json({
    success: true,
    customers: mockData.customers,
    total: mockData.customers.length,
    page: 1,
    per_page: 10
  });
});

app.post('/api/customers', (req, res) => {
  const newCustomer = {
    id: mockData.customers.length + 1,
    ...req.body,
    created_at: new Date().toISOString()
  };
  mockData.customers.push(newCustomer);
  res.json({ success: true, data: newCustomer });
});

// 產品管理 API
app.get('/api/products', (req, res) => {
  res.json({
    success: true,
    products: mockData.products,
    total: mockData.products.length,
    page: 1,
    per_page: 10
  });
});

app.post('/api/products', (req, res) => {
  const newProduct = {
    id: mockData.products.length + 1,
    ...req.body,
    created_at: new Date().toISOString()
  };
  mockData.products.push(newProduct);
  res.json({ success: true, data: newProduct });
});

// 報價單管理 API
app.get('/api/quotations', (req, res) => {
  res.json({
    success: true,
    quotations: mockData.quotations,
    total: mockData.quotations.length,
    page: 1,
    per_page: 10
  });
});

app.post('/api/quotations', (req, res) => {
  const newQuotation = {
    id: mockData.quotations.length + 1,
    quotation_no: `QT-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}-${String(mockData.quotations.length + 1).padStart(3, '0')}`,
    ...req.body,
    created_at: new Date().toISOString()
  };
  mockData.quotations.push(newQuotation);
  res.json({ success: true, data: newQuotation });
});

// 發票管理 API
app.get('/api/invoices', (req, res) => {
  res.json({
    success: true,
    invoices: mockData.invoices,
    total: mockData.invoices.length,
    page: 1,
    per_page: 10
  });
});

// 送貨單管理 API
app.get('/api/delivery-notes', (req, res) => {
  res.json({
    success: true,
    delivery_notes: mockData.delivery_notes,
    total: mockData.delivery_notes.length,
    page: 1,
    per_page: 10
  });
});

// 統計 API
app.get('/api/statistics/dashboard', (req, res) => {
  res.json({
    success: true,
    statistics: {
      total_customers: mockData.customers.length,
      active_products: mockData.products.length,
      total_quotations: mockData.quotations.length,
      total_invoices: mockData.invoices.length,
      total_delivery_notes: mockData.delivery_notes.length,
      monthly_quotations: 1,
      monthly_invoices: 0,
      total_revenue: 25000.00,
      monthly_revenue: 5000.00,
      pending_quotations: 1,
      pending_invoices: 0,
      pending_deliveries: 0
    }
  });
});

// 404 處理
app.use((req, res) => {
  res.status(404).json({ 
    success: false, 
    error: `API 端點不存在: ${req.method} ${req.path}` 
  });
});

app.listen(PORT, () => {
  console.log(`🚀 蒼藍工程公司管理系統 - 模擬後端服務器`);
  console.log(`📍 API 地址: http://localhost:${PORT}/api`);
  console.log(`🔍 健康檢查: http://localhost:${PORT}/api/system/health`);
  console.log(`👤 測試帳號: admin / admin123`);
  console.log(`🎯 按 Ctrl+C 停止服務器`);
  console.log(`\n📊 可用的 API 端點:`);
  console.log(`   POST /api/auth/login - 用戶登入`);
  console.log(`   GET  /api/system/health - 健康檢查`);
  console.log(`   GET  /api/system/info - 系統資訊`);
  console.log(`   GET  /api/customers - 客戶列表`);
  console.log(`   GET  /api/products - 產品列表`);
  console.log(`   GET  /api/quotations - 報價單列表`);
  console.log(`   GET  /api/statistics/dashboard - 統計數據`);
}); 