<template>
  <div class="delivery-note-form">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h3>{{ isEdit ? $t('deliveryNote.editDeliveryNote') : $t('deliveryNote.createDeliveryNote') }}</h3>
          <div class="header-actions">
            <el-button @click="goBack" :icon="ArrowLeft">
              {{ $t('common.back') }}
            </el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="default"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="24">
          <!-- 基本信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('deliveryNote.basicInfo') }}</h4>
          </el-col>
          
          <el-col :span="12">
            <el-form-item :label="$t('deliveryNote.quotationId')" prop="quotationId">
              <el-select
                v-model="formData.quotationId"
                :placeholder="$t('deliveryNote.selectQuotation')"
                style="width: 100%"
                @change="handleQuotationChange"
                :disabled="!!route.query.quotationId"
              >
                <el-option
                  v-for="quotation in quotations"
                  :key="quotation.id"
                  :value="quotation.id"
                  :label="`${quotation.quotationNumber} - ${quotation.customerName}`"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('deliveryNote.status')" prop="status">
              <el-select
                v-model="formData.status"
                :placeholder="$t('deliveryNote.selectStatus')"
                style="width: 100%"
              >
                <el-option
                  value="pending"
                  :label="$t('deliveryNote.pending')"
                />
                <el-option
                  value="preparing"
                  :label="$t('deliveryNote.preparing')"
                />
                <el-option
                  value="shipped"
                  :label="$t('deliveryNote.shipped')"
                />
                <el-option
                  value="delivered"
                  :label="$t('deliveryNote.delivered')"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('deliveryNote.number')" prop="deliveryNumber">
              <el-input
                v-model="formData.deliveryNumber"
                :placeholder="$t('deliveryNote.numberPlaceholder')"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('deliveryNote.deliveryDate')" prop="deliveryDate">
              <el-date-picker
                v-model="formData.deliveryDate"
                type="date"
                :placeholder="$t('deliveryNote.deliveryDatePlaceholder')"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>

          <!-- 送貨信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('deliveryNote.deliveryInfo') }}</h4>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('deliveryNote.deliveryAddress')" prop="deliveryAddress">
              <el-input
                v-model="formData.deliveryAddress"
                :placeholder="$t('deliveryNote.deliveryAddressPlaceholder')"
                type="textarea"
                :rows="3"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('deliveryNote.contactPerson')" prop="contactPerson">
              <el-input
                v-model="formData.contactPerson"
                :placeholder="$t('deliveryNote.contactPersonPlaceholder')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('deliveryNote.contactPhone')" prop="contactPhone">
              <el-input
                v-model="formData.contactPhone"
                :placeholder="$t('deliveryNote.contactPhonePlaceholder')"
                maxlength="20"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('deliveryNote.deliveryMethod')" prop="deliveryMethod">
              <el-select
                v-model="formData.deliveryMethod"
                :placeholder="$t('deliveryNote.selectDeliveryMethod')"
                style="width: 100%"
              >
                <el-option value="pickup" :label="$t('deliveryNote.pickup')" />
                <el-option value="delivery" :label="$t('deliveryNote.delivery')" />
                <el-option value="courier" :label="$t('deliveryNote.courier')" />
                <el-option value="freight" :label="$t('deliveryNote.freight')" />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 客戶信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('deliveryNote.customerInfo') }}</h4>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.name')" prop="customerName">
              <el-input
                v-model="formData.customerName"
                :placeholder="$t('customer.name')"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.email')" prop="customerEmail">
              <el-input
                v-model="formData.customerEmail"
                :placeholder="$t('customer.email')"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.phone')" prop="customerPhone">
              <el-input
                v-model="formData.customerPhone"
                :placeholder="$t('customer.phone')"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.address')" prop="customerAddress">
              <el-input
                v-model="formData.customerAddress"
                :placeholder="$t('customer.address')"
                type="textarea"
                :rows="2"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <!-- 送貨項目 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('deliveryNote.items') }}</h4>
          </el-col>

          <el-col :span="24">
            <div class="items-table">
              <el-table
                :data="formData.items"
                border
                size="small"
                style="width: 100%"
                max-height="300"
              >
                <el-table-column
                  prop="productName"
                  :label="$t('product.name')"
                  min-width="150"
                />
                <el-table-column
                  prop="description"
                  :label="$t('product.description')"
                  min-width="200"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="quantity"
                  :label="$t('quotation.quantity')"
                  width="100"
                  align="center"
                />
                <el-table-column
                  prop="unit"
                  :label="$t('product.unit')"
                  width="80"
                  align="center"
                />
                <el-table-column
                  :label="$t('deliveryNote.actualQuantity')"
                  width="120"
                  align="center"
                >
                  <template #default="{ row, $index }">
                    <el-input-number
                      v-model="row.actualQuantity"
                      :min="0"
                      :max="row.quantity"
                      :precision="0"
                      size="small"
                      style="width: 100%"
                      @change="updateActualQuantity($index)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>

          <!-- 特殊說明 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('deliveryNote.specialInstructions') }}</h4>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('deliveryNote.deliveryInstructions')" prop="deliveryInstructions">
              <el-input
                v-model="formData.deliveryInstructions"
                :placeholder="$t('deliveryNote.deliveryInstructionsPlaceholder')"
                type="textarea"
                :rows="3"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('deliveryNote.notes')" prop="notes">
              <el-input
                v-model="formData.notes"
                :placeholder="$t('deliveryNote.notesPlaceholder')"
                type="textarea"
                :rows="4"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <!-- 簽收信息 -->
          <el-col :span="24" v-if="formData.status === 'delivered'">
            <h4 class="section-title">{{ $t('deliveryNote.signatureInfo') }}</h4>
          </el-col>

          <el-col :span="12" v-if="formData.status === 'delivered'">
            <el-form-item :label="$t('deliveryNote.receivedBy')" prop="receivedBy">
              <el-input
                v-model="formData.receivedBy"
                :placeholder="$t('deliveryNote.receivedByPlaceholder')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="formData.status === 'delivered'">
            <el-form-item :label="$t('deliveryNote.receivedDate')" prop="receivedDate">
              <el-date-picker
                v-model="formData.receivedDate"
                type="datetime"
                :placeholder="$t('deliveryNote.receivedDatePlaceholder')"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 表單操作按鈕 -->
        <div class="form-actions">
          <el-button @click="goBack">
            {{ $t('common.cancel') }}
          </el-button>
          <el-button @click="handlePreview" :icon="View">
            {{ $t('deliveryNote.preview') }}
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ $t('common.save') }}
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { ArrowLeft, View } from '@element-plus/icons-vue';
import { useQuotationStore } from '@/stores/quotation';
import { validationRules } from '@/utils';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const quotationStore = useQuotationStore();

// 表單引用
const formRef = ref(null);

// 狀態
const submitting = ref(false);
const isEdit = computed(() => !!route.params.id);

// 已批准的報價單列表
const quotations = computed(() => 
  quotationStore.quotations.filter(q => q.status === 'approved')
);

// 表單數據
const formData = reactive({
  quotationId: '',
  status: 'pending',
  deliveryNumber: '',
  deliveryDate: '',
  deliveryAddress: '',
  contactPerson: '',
  contactPhone: '',
  deliveryMethod: 'delivery',
  customerName: '',
  customerEmail: '',
  customerPhone: '',
  customerAddress: '',
  items: [],
  deliveryInstructions: '',
  notes: '',
  receivedBy: '',
  receivedDate: ''
});

// 表單驗證規則
const formRules = computed(() => ({
  quotationId: [validationRules.required()],
  status: [validationRules.required()],
  deliveryNumber: [
    validationRules.required(),
    validationRules.minLength(3),
    validationRules.maxLength(20)
  ],
  deliveryDate: [validationRules.required()],
  deliveryAddress: [
    validationRules.required(),
    validationRules.maxLength(500)
  ],
  contactPerson: [
    validationRules.required(),
    validationRules.maxLength(50)
  ],
  contactPhone: [
    validationRules.required(),
    validationRules.phone()
  ],
  deliveryMethod: [validationRules.required()],
  deliveryInstructions: [validationRules.maxLength(500)],
  notes: [validationRules.maxLength(1000)],
  receivedBy: formData.status === 'delivered' ? [
    validationRules.required(),
    validationRules.maxLength(50)
  ] : [],
  receivedDate: formData.status === 'delivered' ? [
    validationRules.required()
  ] : []
}));

// 處理報價單變更
const handleQuotationChange = async (quotationId) => {
  if (!quotationId) return;

  const quotation = quotations.value.find(q => q.id === quotationId);
  if (quotation) {
    // 填充客戶信息
    formData.customerName = quotation.customerName;
    formData.customerEmail = quotation.customerEmail;
    formData.customerPhone = quotation.customerPhone;
    formData.customerAddress = quotation.customerAddress;
    
    // 複製項目並添加實際數量字段
    formData.items = quotation.items.map(item => ({
      ...item,
      actualQuantity: item.quantity
    }));
    
    // 設置默認送貨地址
    if (!formData.deliveryAddress) {
      formData.deliveryAddress = quotation.customerAddress;
    }
  }
};

// 更新實際數量
const updateActualQuantity = (index) => {
  // 確保實際數量不超過訂購數量
  const item = formData.items[index];
  if (item.actualQuantity > item.quantity) {
    item.actualQuantity = item.quantity;
  }
};

// 預覽送貨單
const handlePreview = () => {
  // TODO: 實現送貨單預覽功能
  ElMessage.info(t('deliveryNote.previewFeature'));
};

// 返回列表頁
const goBack = () => {
  router.push('/delivery-notes');
};

// 提交表單
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    // TODO: 實現送貨單保存邏輯
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success(
      isEdit.value ? t('deliveryNote.updateSuccess') : t('deliveryNote.createSuccess')
    );
    goBack();
  } catch (error) {
    console.error('Form validation failed:', error);
  } finally {
    submitting.value = false;
  }
};

// 自動生成送貨單號碼
const generateDeliveryNumber = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const time = String(now.getTime()).slice(-6);
  return `DN${year}${month}${day}${time}`;
};

// 設置默認日期
const setDefaultDate = () => {
  const today = new Date();
  const deliveryDate = new Date(today);
  deliveryDate.setDate(today.getDate() + 3); // 3天後送貨
  
  formData.deliveryDate = deliveryDate.toISOString().split('T')[0];
};

onMounted(() => {
  // 從查詢參數獲取報價單ID
  if (route.query.quotationId) {
    formData.quotationId = route.query.quotationId;
    handleQuotationChange(route.query.quotationId);
  }

  // 設置默認值
  if (!isEdit.value) {
    formData.deliveryNumber = generateDeliveryNumber();
    setDefaultDate();
  }
});
</script>

<style scoped>
.delivery-note-form {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.section-title {
  color: #409EFF;
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.items-table {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.form-actions .el-button {
  min-width: 120px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .delivery-note-form {
    padding: 12px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style> 