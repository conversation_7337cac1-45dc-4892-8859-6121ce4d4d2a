// 簡化的 API 客戶端
import axios from 'axios';

// 創建 axios 實例
const apiClient = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 請求攔截器
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 響應攔截器
apiClient.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 認證 API
export const authAPI = {
  login: (credentials) => {
    return apiClient.post('/auth/login', credentials);
  },
  
  logout: () => {
    return apiClient.post('/auth/logout');
  },
  
  getUserInfo: () => {
    return apiClient.get('/auth/me');
  }
};

// 報價單 API
export const quotationAPI = {
  getList: (params) => {
    return apiClient.get('/quotations', { params });
  },
  
  getById: (id) => {
    return apiClient.get(`/quotations/${id}`);
  },
  
  create: (data) => {
    return apiClient.post('/quotations', data);
  },
  
  update: (id, data) => {
    return apiClient.put(`/quotations/${id}`, data);
  },
  
  delete: (id) => {
    return apiClient.delete(`/quotations/${id}`);
  }
};

// 客戶 API
export const customerAPI = {
  getList: (params) => {
    return apiClient.get('/customers', { params });
  },
  
  getById: (id) => {
    return apiClient.get(`/customers/${id}`);
  },
  
  create: (data) => {
    return apiClient.post('/customers', data);
  },
  
  update: (id, data) => {
    return apiClient.put(`/customers/${id}`, data);
  },
  
  delete: (id) => {
    return apiClient.delete(`/customers/${id}`);
  }
};

// 產品 API
export const productAPI = {
  getList: (params) => {
    return apiClient.get('/products', { params });
  },
  
  getById: (id) => {
    return apiClient.get(`/products/${id}`);
  },
  
  create: (data) => {
    return apiClient.post('/products', data);
  },
  
  update: (id, data) => {
    return apiClient.put(`/products/${id}`, data);
  },
  
  delete: (id) => {
    return apiClient.delete(`/products/${id}`);
  }
};

export default apiClient;
