// 蒼藍工程公司管理系統 - 主樣式文件

// 變數定義
:root {
  // 主色調
  --primary-color: #409EFF;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;
  
  // 輔助色
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  
  // 中性色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;
  
  // 邊框色
  --border-base: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
  --border-extra-light: #F2F6FC;
  
  // 背景色
  --bg-color: #FFFFFF;
  --bg-page: #F2F3F5;
  --bg-overlay: rgba(255, 255, 255, 0.9);
  
  // 陰影
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  // 圓角
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  --border-radius-round: 20px;
  
  // 間距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  // 字體大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  // 行高
  --line-height-base: 1.5;
  --line-height-small: 1.2;
  --line-height-large: 1.8;
}

// 全局樣式重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 14px;
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background-color: var(--bg-page);
}

body {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", 
               "Microsoft YaHei", "微軟雅黑", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 應用程式容器
#app {
  height: 100vh;
  overflow: hidden;
}

// 通用工具類
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

// 間距工具類
@for $i from 0 through 10 {
  .m-#{$i} { margin: #{$i * 4}px; }
  .mt-#{$i} { margin-top: #{$i * 4}px; }
  .mr-#{$i} { margin-right: #{$i * 4}px; }
  .mb-#{$i} { margin-bottom: #{$i * 4}px; }
  .ml-#{$i} { margin-left: #{$i * 4}px; }
  .mx-#{$i} { margin-left: #{$i * 4}px; margin-right: #{$i * 4}px; }
  .my-#{$i} { margin-top: #{$i * 4}px; margin-bottom: #{$i * 4}px; }
  
  .p-#{$i} { padding: #{$i * 4}px; }
  .pt-#{$i} { padding-top: #{$i * 4}px; }
  .pr-#{$i} { padding-right: #{$i * 4}px; }
  .pb-#{$i} { padding-bottom: #{$i * 4}px; }
  .pl-#{$i} { padding-left: #{$i * 4}px; }
  .px-#{$i} { padding-left: #{$i * 4}px; padding-right: #{$i * 4}px; }
  .py-#{$i} { padding-top: #{$i * 4}px; padding-bottom: #{$i * 4}px; }
}

// 彈性布局
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }
.flex-1 { flex: 1; }
.flex-wrap { flex-wrap: wrap; }

// 顯示/隱藏
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }

// 位置
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

// 溢出
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

// 寬度和高度
.w-100 { width: 100%; }
.h-100 { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

// 圓角
.rounded { border-radius: var(--border-radius-base); }
.rounded-sm { border-radius: var(--border-radius-small); }
.rounded-lg { border-radius: var(--border-radius-large); }
.rounded-full { border-radius: 50%; }

// 陰影
.shadow { box-shadow: var(--shadow-base); }
.shadow-light { box-shadow: var(--shadow-light); }
.shadow-dark { box-shadow: var(--shadow-dark); }
.shadow-none { box-shadow: none; }

// 邊框
.border { border: 1px solid var(--border-base); }
.border-top { border-top: 1px solid var(--border-base); }
.border-right { border-right: 1px solid var(--border-base); }
.border-bottom { border-bottom: 1px solid var(--border-base); }
.border-left { border-left: 1px solid var(--border-base); }
.border-none { border: none; }

// 自定義組件樣式
.page-container {
  padding: var(--spacing-lg);
  background-color: var(--bg-page);
  min-height: 100vh;
}

.content-card {
  background-color: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-color);
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-lighter);
}

.table-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: var(--border-radius-round);
  font-size: var(--font-size-xs);
  font-weight: 500;
  
  &.status-active {
    background-color: #f0f9ff;
    color: var(--success-color);
    border: 1px solid #d1fae5;
  }
  
  &.status-inactive {
    background-color: #fef2f2;
    color: var(--danger-color);
    border: 1px solid #fecaca;
  }
  
  &.status-pending {
    background-color: #fffbeb;
    color: var(--warning-color);
    border: 1px solid #fed7aa;
  }
  
  &.status-approved {
    background-color: #f0fdf4;
    color: var(--success-color);
    border: 1px solid #bbf7d0;
  }
}

// 響應式設計
@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md);
  }
  
  .content-card {
    padding: var(--spacing-md);
  }
  
  .form-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
  
  .table-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
      margin-left: 0 !important;
      margin-bottom: var(--spacing-xs);
    }
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
  
  .section-title {
    font-size: var(--font-size-base);
  }
}

// 打印樣式
@media print {
  .no-print {
    display: none !important;
  }
  
  .page-container {
    padding: 0;
    background-color: white;
  }
  
  .content-card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

// 深色模式支持（預留）
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #E4E7ED;
    --text-regular: #CFD3DC;
    --text-secondary: #A4A9B6;
    --bg-color: #2D3748;
    --bg-page: #1A202C;
    --border-base: #4A5568;
  }
}

// Element Plus 自定義樣式
.el-card {
  border: none;
  box-shadow: var(--shadow-light);
}

.el-table {
  .el-table__header {
    background-color: var(--bg-page);
  }
}

.el-form-item__label {
  font-weight: 500;
  color: var(--text-primary);
}

.el-button {
  border-radius: var(--border-radius-base);
  font-weight: 500;
}

.el-input__inner,
.el-textarea__inner {
  border-radius: var(--border-radius-base);
}

// 滾動條樣式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-page);
  border-radius: var(--border-radius-base);
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: var(--border-radius-base);
  
  &:hover {
    background: var(--text-secondary);
  }
}

// 動畫效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
} 