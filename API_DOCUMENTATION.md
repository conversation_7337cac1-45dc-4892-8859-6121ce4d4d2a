# 蒼藍工程公司管理系統 - API 文檔
## H.B Engineering Company Management System - API Documentation

### 基本資訊

- **基礎URL**: `http://[樹莓派IP地址]:5000/api`
- **認證方式**: JWT Bearer Token
- **Content-Type**: `application/json`
- **字符編碼**: UTF-8

### 認證說明

除了系統資訊和健康檢查API外，所有API都需要在Header中包含認證Token：

```
Authorization: Bearer <access_token>
```

### 回應格式

所有API回應都包含以下標準格式：

```json
{
    "success": true,
    "message": "操作成功",
    "data": { /* 具體數據 */ },
    "error": null
}
```

錯誤回應格式：
```json
{
    "success": false,
    "message": null,
    "data": null,
    "error": "錯誤訊息"
}
```

---

## 1. 認證相關 API

### 1.1 用戶登入
- **URL**: `POST /auth/login`
- **說明**: 用戶登入獲取訪問令牌
- **認證**: 不需要

**請求參數:**
```json
{
    "username": "admin",
    "password": "admin123"
}
```

**成功回應:**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin"
    },
    "success": true
}
```

### 1.2 註冊新用戶
- **URL**: `POST /auth/register`
- **說明**: 註冊新用戶（需要管理員權限）
- **認證**: 需要（管理員）

**請求參數:**
```json
{
    "username": "newuser",
    "password": "password123",
    "email": "<EMAIL>",
    "role": "user"
}
```

---

## 2. 客戶管理 API

### 2.1 獲取客戶列表
- **URL**: `GET /customers`
- **說明**: 分頁獲取客戶列表
- **認證**: 需要

**查詢參數:**
- `page`: 頁碼（預設：1）
- `per_page`: 每頁數量（預設：10）
- `search`: 搜索關鍵字（可選）

**回應範例:**
```json
{
    "customers": [
        {
            "id": 1,
            "name": "ABC電子有限公司",
            "contact_person": "張先生",
            "phone": "+852 12345678",
            "email": "<EMAIL>",
            "address": "香港九龍...",
            "company_reg_no": "12345678",
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-01T00:00:00"
        }
    ],
    "total": 50,
    "page": 1,
    "per_page": 10,
    "success": true
}
```

### 2.2 創建客戶
- **URL**: `POST /customers`
- **說明**: 創建新客戶
- **認證**: 需要

**請求參數:**
```json
{
    "name": "XYZ工程公司",
    "contact_person": "李小姐",
    "phone": "+852 87654321",
    "email": "<EMAIL>",
    "address": "香港島...",
    "company_reg_no": "87654321"
}
```

### 2.3 更新客戶
- **URL**: `PUT /customers/{customer_id}`
- **說明**: 更新客戶資訊
- **認證**: 需要

### 2.4 刪除客戶
- **URL**: `DELETE /customers/{customer_id}`
- **說明**: 刪除客戶
- **認證**: 需要

---

## 3. 產品管理 API

### 3.1 獲取產品列表
- **URL**: `GET /products`
- **說明**: 分頁獲取產品列表
- **認證**: 需要

**查詢參數:**
- `page`: 頁碼（預設：1）
- `per_page`: 每頁數量（預設：10）
- `search`: 搜索關鍵字（可選）
- `category`: 產品分類（可選）

**回應範例:**
```json
{
    "products": [
        {
            "id": 1,
            "name": "LED燈具",
            "description": "高效能LED照明設備",
            "unit": "pc",
            "unit_price": 150.00,
            "category": "照明設備",
            "is_active": true,
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-01T00:00:00"
        }
    ],
    "total": 25,
    "page": 1,
    "per_page": 10,
    "success": true
}
```

### 3.2 創建產品
- **URL**: `POST /products`
- **說明**: 創建新產品
- **認證**: 需要

**請求參數:**
```json
{
    "name": "電纜線",
    "description": "高品質電纜線材",
    "unit": "m",
    "unit_price": 25.50,
    "category": "電線電纜"
}
```

---

## 4. 報價單管理 API

### 4.1 獲取報價單列表
- **URL**: `GET /quotations`
- **說明**: 分頁獲取報價單列表
- **認證**: 需要

**查詢參數:**
- `page`: 頁碼（預設：1）
- `per_page`: 每頁數量（預設：10）
- `status`: 狀態篩選（可選：draft, sent, accepted, rejected）

**回應範例:**
```json
{
    "quotations": [
        {
            "id": 1,
            "quotation_no": "QT-202401-0001",
            "customer_id": 1,
            "customer_name": "ABC電子有限公司",
            "total_amount": 1000.00,
            "discount_rate": 5.0,
            "discount_amount": 50.00,
            "final_amount": 950.00,
            "status": "draft",
            "valid_until": "2024-02-01",
            "notes": "緊急項目",
            "created_by": 1,
            "created_by_name": "admin",
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-01T00:00:00"
        }
    ],
    "total": 15,
    "page": 1,
    "per_page": 10,
    "success": true
}
```

### 4.2 創建報價單
- **URL**: `POST /quotations`
- **說明**: 創建新報價單
- **認證**: 需要

**請求參數:**
```json
{
    "customer_id": 1,
    "total_amount": 1000.00,
    "discount_rate": 5.0,
    "discount_amount": 50.00,
    "final_amount": 950.00,
    "status": "draft",
    "valid_until": "2024-02-01",
    "notes": "項目備註",
    "items": [
        {
            "product_id": 1,
            "description": "LED燈具安裝",
            "quantity": 10.0,
            "unit_price": 150.00,
            "total_price": 1500.00
        },
        {
            "product_id": null,
            "description": "人工費用",
            "quantity": 1.0,
            "unit_price": 500.00,
            "total_price": 500.00
        }
    ]
}
```

### 4.3 獲取報價單詳情
- **URL**: `GET /quotations/{quotation_id}`
- **說明**: 獲取單個報價單詳細資訊
- **認證**: 需要

**回應範例:**
```json
{
    "quotation": {
        "id": 1,
        "quotation_no": "QT-202401-0001",
        "customer_id": 1,
        "customer_name": "ABC電子有限公司",
        "customer_address": "香港九龍...",
        "customer_phone": "+852 12345678",
        "customer_email": "<EMAIL>",
        "total_amount": 1000.00,
        "discount_rate": 5.0,
        "discount_amount": 50.00,
        "final_amount": 950.00,
        "status": "draft",
        "valid_until": "2024-02-01",
        "notes": "項目備註",
        "created_by": 1,
        "created_by_name": "admin",
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00",
        "items": [
            {
                "id": 1,
                "quotation_id": 1,
                "product_id": 1,
                "product_name": "LED燈具",
                "product_unit": "pc",
                "description": "LED燈具安裝",
                "quantity": 10.0,
                "unit_price": 150.00,
                "total_price": 1500.00,
                "sort_order": 0
            }
        ]
    },
    "success": true
}
```

### 4.4 下載報價單PDF
- **URL**: `GET /quotations/{quotation_id}/pdf`
- **說明**: 下載報價單PDF文件
- **認證**: 需要
- **回應**: 直接返回PDF文件

---

## 5. 發票管理 API

### 5.1 獲取發票列表
- **URL**: `GET /invoices`
- **說明**: 分頁獲取發票列表
- **認證**: 需要

**查詢參數:**
- `page`: 頁碼（預設：1）
- `per_page`: 每頁數量（預設：10）
- `payment_status`: 付款狀態篩選（可選：pending, paid, overdue）

### 5.2 創建發票
- **URL**: `POST /invoices`
- **說明**: 創建新發票
- **認證**: 需要

**請求參數:**
```json
{
    "quotation_id": 1,
    "customer_id": 1,
    "total_amount": 1000.00,
    "tax_rate": 0.0,
    "tax_amount": 0.0,
    "final_amount": 1000.00,
    "payment_status": "pending",
    "payment_due_date": "2024-02-01",
    "notes": "請於到期日前付款"
}
```

### 5.3 更新付款狀態
- **URL**: `PUT /invoices/{invoice_id}/payment`
- **說明**: 更新發票付款狀態
- **認證**: 需要

**請求參數:**
```json
{
    "payment_status": "paid",
    "payment_received_date": "2024-01-15"
}
```

---

## 6. 送貨單管理 API

### 6.1 獲取送貨單列表
- **URL**: `GET /delivery-notes`
- **說明**: 分頁獲取送貨單列表
- **認證**: 需要

### 6.2 創建送貨單
- **URL**: `POST /delivery-notes`
- **說明**: 創建新送貨單
- **認證**: 需要

**請求參數:**
```json
{
    "invoice_id": 1,
    "customer_id": 1,
    "delivery_date": "2024-01-20",
    "delivery_address": "香港九龍...",
    "delivery_status": "pending",
    "notes": "請準備收貨"
}
```

### 6.3 更新送貨狀態
- **URL**: `PUT /delivery-notes/{delivery_note_id}/status`
- **說明**: 更新送貨狀態
- **認證**: 需要

**請求參數:**
```json
{
    "delivery_status": "delivered",
    "received_by": "張先生",
    "signature_file": "path/to/signature.png"
}
```

---

## 7. 系統設定 API

### 7.1 獲取系統設定
- **URL**: `GET /settings`
- **說明**: 獲取公司及系統設定
- **認證**: 需要

**回應範例:**
```json
{
    "settings": {
        "company_name_cn": "蒼藍工程公司",
        "company_name_en": "H.B ENGINEERING COMPANY",
        "company_address_cn": "景松樓 景林邨 寶林將軍澳",
        "company_address_en": "King Chung House, King Lam Estate, Po Lam, Tseung Kwan O",
        "company_nature_cn": "電力工程",
        "company_nature_en": "Electrical Engineering",
        "company_phone": "+852 XXXXXXXX",
        "company_email": "<EMAIL>"
    },
    "success": true
}
```

### 7.2 更新系統設定
- **URL**: `PUT /settings`
- **說明**: 更新公司及系統設定
- **認證**: 需要

**請求參數:**
```json
{
    "company_name_cn": "蒼藍工程公司",
    "company_phone": "+852 12345678",
    "company_email": "<EMAIL>"
}
```

---

## 8. 系統資訊 API

### 8.1 系統資訊
- **URL**: `GET /system/info`
- **說明**: 獲取系統基本資訊
- **認證**: 不需要

**回應範例:**
```json
{
    "system": "H.B Engineering Management System",
    "version": "2.0.0",
    "backend": "Python Flask",
    "database": "SQLite",
    "platform": "Raspberry Pi",
    "success": true
}
```

### 8.2 健康檢查
- **URL**: `GET /system/health`
- **說明**: 系統健康狀態檢查
- **認證**: 不需要

**回應範例:**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "database": "connected",
    "success": true
}
```

---

## 錯誤代碼說明

| HTTP狀態碼 | 說明 |
|-----------|------|
| 200 | 成功 |
| 400 | 請求參數錯誤 |
| 401 | 未授權或token無效 |
| 403 | 權限不足 |
| 404 | 資源不存在 |
| 500 | 服務器內部錯誤 |

---

## 前端整合範例

### JavaScript/TypeScript 整合範例

```javascript
// API基礎配置
const API_BASE_URL = 'http://*************:5000/api';

class HBEngineeringAPI {
    constructor() {
        this.token = localStorage.getItem('access_token');
    }

    // 設定認證Token
    setToken(token) {
        this.token = token;
        localStorage.setItem('access_token', token);
    }

    // 通用請求方法
    async request(endpoint, options = {}) {
        const url = `${API_BASE_URL}${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        try {
            const response = await fetch(url, {
                ...options,
                headers
            });

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || '請求失敗');
            }

            return data;
        } catch (error) {
            console.error('API請求錯誤:', error);
            throw error;
        }
    }

    // 用戶登入
    async login(username, password) {
        const response = await this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify({ username, password })
        });
        
        if (response.success) {
            this.setToken(response.access_token);
        }
        
        return response;
    }

    // 獲取客戶列表
    async getCustomers(page = 1, perPage = 10, search = '') {
        const params = new URLSearchParams({
            page: page.toString(),
            per_page: perPage.toString(),
            search
        });

        return await this.request(`/customers?${params}`);
    }

    // 創建報價單
    async createQuotation(quotationData) {
        return await this.request('/quotations', {
            method: 'POST',
            body: JSON.stringify(quotationData)
        });
    }

    // 下載PDF
    async downloadQuotationPDF(quotationId) {
        const response = await fetch(`${API_BASE_URL}/quotations/${quotationId}/pdf`, {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `quotation_${quotationId}.pdf`;
            a.click();
            URL.revokeObjectURL(url);
        }
    }
}

// 使用範例
const api = new HBEngineeringAPI();

// 登入
api.login('admin', 'admin123').then(response => {
    console.log('登入成功:', response.user);
}).catch(error => {
    console.error('登入失敗:', error);
});

// 獲取客戶列表
api.getCustomers(1, 10).then(response => {
    console.log('客戶列表:', response.customers);
});
```

---

## Electron 前端整合注意事項

1. **CORS設定**: 後端已配置允許跨域請求，前端可直接調用
2. **安全性**: 在Electron主進程中存儲敏感Token
3. **檔案下載**: 使用Electron的下載管理器處理PDF等檔案
4. **離線模式**: 可配合本地儲存實現離線操作

---

## 測試工具

### 使用curl測試API

```bash
# 登入
curl -X POST http://*************:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 獲取客戶列表（需要替換TOKEN）
curl -X GET http://*************:5000/api/customers \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 健康檢查
curl -X GET http://*************:5000/api/system/health
```

### 使用Postman測試

1. 導入API集合（可根據此文檔創建）
2. 設定環境變數：`baseUrl` = `http://[樹莓派IP]:5000/api`
3. 設定授權方式為Bearer Token
4. 測試各個API端點

---

**注意**: 
- 將 `[樹莓派IP地址]` 替換為實際的樹莓派IP地址
- 生產環境請修改預設密碼和密鑰
- 建議使用HTTPS進行安全通訊
- 定期備份數據庫文件 