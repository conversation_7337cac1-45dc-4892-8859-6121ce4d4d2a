import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useUserStore } from '@stores/user';
import { RateLimiter, generateCSRFToken, validateCSRFToken } from '@/utils/security';
import { ElMessage } from 'element-plus';

// 安全相關組合式函數
export function useSecurity() {
  const userStore = useUserStore();
  const csrfToken = ref('');
  const sessionTimeout = ref(30 * 60 * 1000); // 30分鐘
  const lastActivity = ref(Date.now());
  const isSessionActive = ref(true);

  // 生成 CSRF Token
  const generateToken = () => {
    csrfToken.value = generateCSRFToken();
    return csrfToken.value;
  };

  // 驗證 CSRF Token
  const validateToken = (token: string) => {
    return validateCSRFToken(token, csrfToken.value);
  };

  // 更新最後活動時間
  const updateActivity = () => {
    lastActivity.value = Date.now();
    isSessionActive.value = true;
  };

  // 檢查會話是否過期
  const checkSessionExpiry = () => {
    const now = Date.now();
    const timeSinceLastActivity = now - lastActivity.value;
    
    if (timeSinceLastActivity > sessionTimeout.value) {
      isSessionActive.value = false;
      ElMessage.warning('會話已過期，請重新登入');
      userStore.logout();
      return false;
    }
    return true;
  };

  // 設置活動監聽器
  const setupActivityListeners = () => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  };

  // 會話倒計時
  const sessionCountdown = computed(() => {
    if (!isSessionActive.value) return 0;
    const remaining = sessionTimeout.value - (Date.now() - lastActivity.value);
    return Math.max(0, Math.floor(remaining / 1000));
  });

  onMounted(() => {
    generateToken();
    const cleanup = setupActivityListeners();
    
    // 定期檢查會話狀態
    const interval = setInterval(checkSessionExpiry, 60000); // 每分鐘檢查一次
    
    onUnmounted(() => {
      cleanup();
      clearInterval(interval);
    });
  });

  return {
    csrfToken,
    sessionTimeout,
    isSessionActive,
    sessionCountdown,
    generateToken,
    validateToken,
    updateActivity,
    checkSessionExpiry,
  };
}

// 權限檢查組合式函數
export function usePermissions() {
  const userStore = useUserStore();

  const hasPermission = (permission: string): boolean => {
    return userStore.hasPermission(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return userStore.hasAnyPermission(permissions);
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return userStore.hasAllPermissions(permissions);
  };

  const requirePermission = (permission: string): boolean => {
    if (!hasPermission(permission)) {
      ElMessage.error('您沒有執行此操作的權限');
      return false;
    }
    return true;
  };

  const requireAnyPermission = (permissions: string[]): boolean => {
    if (!hasAnyPermission(permissions)) {
      ElMessage.error('您沒有執行此操作的權限');
      return false;
    }
    return true;
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    requirePermission,
    requireAnyPermission,
  };
}

// 速率限制組合式函數
export function useRateLimit(key: string, maxRequests: number = 10, windowMs: number = 60000) {
  const isAllowed = (): boolean => {
    return RateLimiter.isAllowed(key, maxRequests, windowMs);
  };

  const checkLimit = (): boolean => {
    if (!isAllowed()) {
      ElMessage.warning('操作過於頻繁，請稍後再試');
      return false;
    }
    return true;
  };

  const reset = (): void => {
    RateLimiter.reset(key);
  };

  return {
    isAllowed,
    checkLimit,
    reset,
  };
}

// 安全輸入組合式函數
export function useSecureInput() {
  const sanitizeInput = (input: string): string => {
    // 移除潛在的惡意字符
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  };

  const validateInput = (input: string, rules: any[]): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    for (const rule of rules) {
      if (rule.required && (!input || input.trim() === '')) {
        errors.push(rule.message || '此欄位為必填');
        continue;
      }
      
      if (rule.minLength && input.length < rule.minLength) {
        errors.push(rule.message || `最少需要 ${rule.minLength} 個字符`);
      }
      
      if (rule.maxLength && input.length > rule.maxLength) {
        errors.push(rule.message || `最多允許 ${rule.maxLength} 個字符`);
      }
      
      if (rule.pattern && !rule.pattern.test(input)) {
        errors.push(rule.message || '格式不正確');
      }
      
      if (rule.validator && !rule.validator(input)) {
        errors.push(rule.message || '驗證失敗');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  return {
    sanitizeInput,
    validateInput,
  };
}

// 文件安全檢查組合式函數
export function useFileSecurityCheck() {
  const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  const allowedDocumentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];
  
  const maxImageSize = 5 * 1024 * 1024; // 5MB
  const maxDocumentSize = 10 * 1024 * 1024; // 10MB

  const validateFile = (file: File): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    // 檢查文件類型
    const isImage = allowedImageTypes.includes(file.type);
    const isDocument = allowedDocumentTypes.includes(file.type);
    
    if (!isImage && !isDocument) {
      errors.push('不支持的文件類型');
    }
    
    // 檢查文件大小
    const maxSize = isImage ? maxImageSize : maxDocumentSize;
    if (file.size > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024);
      errors.push(`文件大小不能超過 ${maxSizeMB}MB`);
    }
    
    // 檢查文件名
    if (file.name.length > 255) {
      errors.push('文件名過長');
    }
    
    // 檢查危險文件擴展名
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    if (dangerousExtensions.includes(fileExtension)) {
      errors.push('不允許上傳可執行文件');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  const scanFileContent = async (file: File): Promise<{ isSafe: boolean; threats: string[] }> => {
    const threats: string[] = [];
    
    try {
      // 讀取文件內容進行基本掃描
      const content = await file.text();
      
      // 檢查惡意腳本
      const scriptPatterns = [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /vbscript:/gi,
        /on\w+\s*=/gi,
      ];
      
      for (const pattern of scriptPatterns) {
        if (pattern.test(content)) {
          threats.push('檢測到潛在的腳本注入');
          break;
        }
      }
      
      // 檢查 SQL 注入模式
      const sqlPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)/gi,
        /(UNION\s+SELECT)/gi,
        /(\bOR\s+1\s*=\s*1\b)/gi,
      ];
      
      for (const pattern of sqlPatterns) {
        if (pattern.test(content)) {
          threats.push('檢測到潛在的 SQL 注入');
          break;
        }
      }
      
    } catch (error) {
      // 如果無法讀取文件內容，跳過內容掃描
    }
    
    return {
      isSafe: threats.length === 0,
      threats,
    };
  };

  return {
    validateFile,
    scanFileContent,
    allowedImageTypes,
    allowedDocumentTypes,
    maxImageSize,
    maxDocumentSize,
  };
}

// 安全日誌記錄組合式函數
export function useSecurityLogger() {
  const logSecurityEvent = (event: {
    type: 'login' | 'logout' | 'permission_denied' | 'suspicious_activity' | 'file_upload' | 'data_access';
    details: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    userId?: number;
    ip?: string;
    userAgent?: string;
  }) => {
    const logEntry = {
      ...event,
      timestamp: new Date().toISOString(),
      userId: event.userId || 'anonymous',
      ip: event.ip || 'unknown',
      userAgent: event.userAgent || navigator.userAgent,
      sessionId: sessionStorage.getItem('sessionId') || 'unknown',
    };
    
    // 發送到後端日誌服務
    console.log('Security Event:', logEntry);
    
    // 在生產環境中，這裡應該調用實際的日誌 API
    // await securityAPI.logEvent(logEntry);
    
    // 對於高危事件，立即通知管理員
    if (event.severity === 'critical') {
      ElMessage.error('檢測到安全威脅，已記錄並通知管理員');
    }
  };

  const logLoginAttempt = (success: boolean, username: string) => {
    logSecurityEvent({
      type: 'login',
      details: `Login attempt for user: ${username}, success: ${success}`,
      severity: success ? 'low' : 'medium',
    });
  };

  const logPermissionDenied = (permission: string, resource: string) => {
    logSecurityEvent({
      type: 'permission_denied',
      details: `Permission denied: ${permission} on ${resource}`,
      severity: 'medium',
    });
  };

  const logSuspiciousActivity = (activity: string) => {
    logSecurityEvent({
      type: 'suspicious_activity',
      details: activity,
      severity: 'high',
    });
  };

  const logFileUpload = (fileName: string, fileSize: number, success: boolean) => {
    logSecurityEvent({
      type: 'file_upload',
      details: `File upload: ${fileName} (${fileSize} bytes), success: ${success}`,
      severity: 'low',
    });
  };

  const logDataAccess = (resource: string, action: string) => {
    logSecurityEvent({
      type: 'data_access',
      details: `Data access: ${action} on ${resource}`,
      severity: 'low',
    });
  };

  return {
    logSecurityEvent,
    logLoginAttempt,
    logPermissionDenied,
    logSuspiciousActivity,
    logFileUpload,
    logDataAccess,
  };
}
