<template>
  <el-container class="layout-container">
    <!-- 側邊欄 -->
    <el-aside :width="isCollapse ? '64px' : '200px'">
      <div class="logo">
        <h3 v-if="!isCollapse" style="color: white; margin: 0;">蒼藍工程</h3>
        <span v-else style="color: white; font-size: 18px; font-weight: bold;">HB</span>
      </div>
      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical"
        :collapse="isCollapse"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router>
        <el-menu-item index="/">
          <el-icon><DataBoard /></el-icon>
          <template #title>儀表板</template>
        </el-menu-item>
        <el-menu-item index="/quotations">
          <el-icon><Document /></el-icon>
          <template #title>報價單管理</template>
        </el-menu-item>
        <el-menu-item index="/invoices">
          <el-icon><Tickets /></el-icon>
          <template #title>發票管理</template>
        </el-menu-item>
        <el-menu-item index="/delivery-notes">
          <el-icon><Box /></el-icon>
          <template #title>送貨單管理</template>
        </el-menu-item>
        <el-menu-item index="/customers">
          <el-icon><User /></el-icon>
          <template #title>客戶管理</template>
        </el-menu-item>
        <el-menu-item index="/products">
          <el-icon><Goods /></el-icon>
          <template #title>產品管理</template>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主要內容區 -->
    <el-container>
      <el-header>
        <div class="header-left">
          <el-button link @click="toggleSidebar">
            <el-icon><Menu /></el-icon>
          </el-button>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path" :to="item.path">
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32">{{ userName.charAt(0) }}</el-avatar>
              <span>{{ userName }}</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  個人設置
                </el-dropdown-item>
                <el-dropdown-item command="language">
                  <el-icon><Switch /></el-icon>
                  切換語言
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  登出
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessageBox } from 'element-plus';
import { 
  DataBoard, Document, Tickets, Box, User, Goods, Menu, 
  Switch, SwitchButton 
} from '@element-plus/icons-vue';

const route = useRoute();
const router = useRouter();
const { t, locale } = useI18n();

// 側邊欄狀態
const isCollapse = ref(false);
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value;
};

// 當前激活的菜單
const activeMenu = computed(() => route.path);

// 麵包屑導航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title);
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }));
});

// 用戶信息
const userName = ref('管理員');

// 下拉菜單命令處理
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      // TODO: 實現個人設置
      break;
    case 'language':
      locale.value = locale.value === 'zh-HK' ? 'en' : 'zh-HK';
      break;
    case 'logout':
      try {
        await ElMessageBox.confirm('確定要登出嗎？', '提示', {
          confirmButtonText: '確定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        localStorage.removeItem('token');
        router.push('/login');
      } catch {
        // 用戶取消登出
      }
      break;
  }
};
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.el-aside {
  background-color: #304156;
  transition: width 0.3s;
}

.logo {
  height: 60px;
  padding: 10px;
  text-align: center;
  background-color: #2b3649;
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-menu-vertical {
  border-right: none;
}

.el-header {
  background-color: #fff;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-info span {
  margin-left: 8px;
}

.el-main {
  padding: 20px;
  background-color: var(--background-color);
}
</style> 