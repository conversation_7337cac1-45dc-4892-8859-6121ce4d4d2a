// API 接口統一管理

import apiClient from './client';
import type { ApiResponse } from './client';
import type { 
  User, 
  LoginCredentials, 
  UserPermissions, 
  AuthResponse,
  ChangePasswordRequest 
} from '@/types/user';
import type { 
  Quotation, 
  QuotationFormData, 
  QuotationFilters,
  QuotationTemplate,
  QuotationHistory,
  QuotationStatistics,
  QuotationExportOptions,
  QuotationBatchOperation 
} from '@/types/quotation';

// 認證相關 API
export const authAPI = {
  // 登入
  login: (credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> => {
    return apiClient.post('/auth/login', credentials, {
      showSuccessMessage: true,
      successMessage: '登入成功',
    });
  },

  // 登出
  logout: (): Promise<ApiResponse<void>> => {
    return apiClient.post('/auth/logout', {}, {
      skipErrorHandler: true,
    });
  },

  // 刷新 Token
  refreshToken: (refreshToken: string): Promise<ApiResponse<{ access_token: string }>> => {
    return apiClient.post('/auth/refresh', { refresh_token: refreshToken }, {
      skipErrorHandler: true,
    });
  },

  // 獲取用戶信息
  getUserInfo: (): Promise<ApiResponse<{ user: User; permissions: UserPermissions[] }>> => {
    return apiClient.get('/auth/me');
  },

  // 更新用戶資料
  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> => {
    return apiClient.put('/auth/profile', data, {
      showSuccessMessage: true,
      successMessage: '資料更新成功',
    });
  },

  // 修改密碼
  changePassword: (data: ChangePasswordRequest): Promise<ApiResponse<void>> => {
    return apiClient.put('/auth/password', data, {
      showSuccessMessage: true,
      successMessage: '密碼修改成功',
    });
  },

  // 檢查會話
  checkSession: (): Promise<ApiResponse<{ valid: boolean }>> => {
    return apiClient.get('/auth/session', {
      skipErrorHandler: true,
    });
  },
};

// 報價單相關 API
export const quotationAPI = {
  // 獲取報價單列表
  getList: (params: any): Promise<ApiResponse<{ items: Quotation[]; total: number }>> => {
    return apiClient.get('/quotations', { params });
  },

  // 獲取報價單詳情
  getById: (id: string | number): Promise<ApiResponse<Quotation>> => {
    return apiClient.get(`/quotations/${id}`);
  },

  // 創建報價單
  create: (data: QuotationFormData): Promise<ApiResponse<Quotation>> => {
    return apiClient.post('/quotations', data, {
      showSuccessMessage: true,
      successMessage: '報價單創建成功',
    });
  },

  // 更新報價單
  update: (id: string | number, data: Partial<Quotation>): Promise<ApiResponse<Quotation>> => {
    return apiClient.put(`/quotations/${id}`, data, {
      showSuccessMessage: true,
      successMessage: '報價單更新成功',
    });
  },

  // 刪除報價單
  delete: (id: string | number): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/quotations/${id}`, {
      showSuccessMessage: true,
      successMessage: '報價單刪除成功',
    });
  },

  // 複製報價單
  duplicate: (id: string | number): Promise<ApiResponse<Quotation>> => {
    return apiClient.post(`/quotations/${id}/duplicate`, {}, {
      showSuccessMessage: true,
      successMessage: '報價單複製成功',
    });
  },

  // 轉換為發票
  convertToInvoice: (id: string | number): Promise<ApiResponse<{ invoice_id: number }>> => {
    return apiClient.post(`/quotations/${id}/convert-to-invoice`, {}, {
      showSuccessMessage: true,
      successMessage: '已成功轉換為發票',
    });
  },

  // 轉換為送貨單
  convertToDeliveryNote: (id: string | number): Promise<ApiResponse<{ delivery_note_id: number }>> => {
    return apiClient.post(`/quotations/${id}/convert-to-delivery-note`, {}, {
      showSuccessMessage: true,
      successMessage: '已成功轉換為送貨單',
    });
  },

  // 生成 PDF
  generatePDF: (id: string | number): Promise<void> => {
    return apiClient.download(`/quotations/${id}/pdf`, `quotation-${id}.pdf`);
  },

  // 發送郵件
  sendEmail: (id: string | number, data: { to: string; subject: string; content: string }): Promise<ApiResponse<void>> => {
    return apiClient.post(`/quotations/${id}/send-email`, data, {
      showSuccessMessage: true,
      successMessage: '郵件發送成功',
    });
  },

  // 獲取歷史記錄
  getHistory: (id: string | number): Promise<ApiResponse<QuotationHistory[]>> => {
    return apiClient.get(`/quotations/${id}/history`);
  },

  // 批量操作
  batchOperation: (data: QuotationBatchOperation): Promise<ApiResponse<void>> => {
    return apiClient.post('/quotations/batch', data, {
      showSuccessMessage: true,
      successMessage: '批量操作完成',
    });
  },

  // 獲取統計數據
  getStatistics: (params?: any): Promise<ApiResponse<QuotationStatistics[]>> => {
    return apiClient.get('/quotations/statistics', { params });
  },

  // 導出數據
  export: (options: QuotationExportOptions): Promise<void> => {
    const filename = `quotations-${new Date().toISOString().split('T')[0]}.${options.format}`;
    return apiClient.download('/quotations/export', filename, {
      method: 'POST',
      data: options,
    });
  },
};

// 客戶相關 API
export const customerAPI = {
  // 獲取客戶列表
  getList: (params: any): Promise<ApiResponse<{ items: any[]; total: number }>> => {
    return apiClient.get('/customers', { params });
  },

  // 獲取客戶詳情
  getById: (id: string | number): Promise<ApiResponse<any>> => {
    return apiClient.get(`/customers/${id}`);
  },

  // 創建客戶
  create: (data: any): Promise<ApiResponse<any>> => {
    return apiClient.post('/customers', data, {
      showSuccessMessage: true,
      successMessage: '客戶創建成功',
    });
  },

  // 更新客戶
  update: (id: string | number, data: any): Promise<ApiResponse<any>> => {
    return apiClient.put(`/customers/${id}`, data, {
      showSuccessMessage: true,
      successMessage: '客戶更新成功',
    });
  },

  // 刪除客戶
  delete: (id: string | number): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/customers/${id}`, {
      showSuccessMessage: true,
      successMessage: '客戶刪除成功',
    });
  },

  // 搜索客戶
  search: (query: string): Promise<ApiResponse<any[]>> => {
    return apiClient.get('/customers/search', { params: { q: query } });
  },
};

// 產品相關 API
export const productAPI = {
  // 獲取產品列表
  getList: (params: any): Promise<ApiResponse<{ items: any[]; total: number }>> => {
    return apiClient.get('/products', { params });
  },

  // 獲取產品詳情
  getById: (id: string | number): Promise<ApiResponse<any>> => {
    return apiClient.get(`/products/${id}`);
  },

  // 創建產品
  create: (data: any): Promise<ApiResponse<any>> => {
    return apiClient.post('/products', data, {
      showSuccessMessage: true,
      successMessage: '產品創建成功',
    });
  },

  // 更新產品
  update: (id: string | number, data: any): Promise<ApiResponse<any>> => {
    return apiClient.put(`/products/${id}`, data, {
      showSuccessMessage: true,
      successMessage: '產品更新成功',
    });
  },

  // 刪除產品
  delete: (id: string | number): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/products/${id}`, {
      showSuccessMessage: true,
      successMessage: '產品刪除成功',
    });
  },

  // 搜索產品
  search: (query: string): Promise<ApiResponse<any[]>> => {
    return apiClient.get('/products/search', { params: { q: query } });
  },

  // 獲取產品分類
  getCategories: (): Promise<ApiResponse<any[]>> => {
    return apiClient.get('/products/categories');
  },
};

// 發票相關 API
export const invoiceAPI = {
  // 獲取發票列表
  getList: (params: any): Promise<ApiResponse<{ items: any[]; total: number }>> => {
    return apiClient.get('/invoices', { params });
  },

  // 獲取發票詳情
  getById: (id: string | number): Promise<ApiResponse<any>> => {
    return apiClient.get(`/invoices/${id}`);
  },

  // 創建發票
  create: (data: any): Promise<ApiResponse<any>> => {
    return apiClient.post('/invoices', data, {
      showSuccessMessage: true,
      successMessage: '發票創建成功',
    });
  },

  // 更新發票
  update: (id: string | number, data: any): Promise<ApiResponse<any>> => {
    return apiClient.put(`/invoices/${id}`, data, {
      showSuccessMessage: true,
      successMessage: '發票更新成功',
    });
  },

  // 刪除發票
  delete: (id: string | number): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/invoices/${id}`, {
      showSuccessMessage: true,
      successMessage: '發票刪除成功',
    });
  },

  // 生成 PDF
  generatePDF: (id: string | number): Promise<void> => {
    return apiClient.download(`/invoices/${id}/pdf`, `invoice-${id}.pdf`);
  },
};

// 送貨單相關 API
export const deliveryNoteAPI = {
  // 獲取送貨單列表
  getList: (params: any): Promise<ApiResponse<{ items: any[]; total: number }>> => {
    return apiClient.get('/delivery-notes', { params });
  },

  // 獲取送貨單詳情
  getById: (id: string | number): Promise<ApiResponse<any>> => {
    return apiClient.get(`/delivery-notes/${id}`);
  },

  // 創建送貨單
  create: (data: any): Promise<ApiResponse<any>> => {
    return apiClient.post('/delivery-notes', data, {
      showSuccessMessage: true,
      successMessage: '送貨單創建成功',
    });
  },

  // 更新送貨單
  update: (id: string | number, data: any): Promise<ApiResponse<any>> => {
    return apiClient.put(`/delivery-notes/${id}`, data, {
      showSuccessMessage: true,
      successMessage: '送貨單更新成功',
    });
  },

  // 刪除送貨單
  delete: (id: string | number): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/delivery-notes/${id}`, {
      showSuccessMessage: true,
      successMessage: '送貨單刪除成功',
    });
  },

  // 生成 PDF
  generatePDF: (id: string | number): Promise<void> => {
    return apiClient.download(`/delivery-notes/${id}/pdf`, `delivery-note-${id}.pdf`);
  },
};

// 文件上傳 API
export const fileAPI = {
  // 上傳文件
  upload: (file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<{ url: string; filename: string }>> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return apiClient.upload('/files/upload', formData, {
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
      showSuccessMessage: true,
      successMessage: '文件上傳成功',
    });
  },

  // 刪除文件
  delete: (filename: string): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/files/${filename}`, {
      showSuccessMessage: true,
      successMessage: '文件刪除成功',
    });
  },
};

// 系統相關 API
export const systemAPI = {
  // 獲取系統信息
  getInfo: (): Promise<ApiResponse<any>> => {
    return apiClient.get('/system/info');
  },

  // 獲取系統設置
  getSettings: (): Promise<ApiResponse<any>> => {
    return apiClient.get('/system/settings');
  },

  // 更新系統設置
  updateSettings: (data: any): Promise<ApiResponse<any>> => {
    return apiClient.put('/system/settings', data, {
      showSuccessMessage: true,
      successMessage: '設置更新成功',
    });
  },

  // 獲取系統日誌
  getLogs: (params: any): Promise<ApiResponse<{ items: any[]; total: number }>> => {
    return apiClient.get('/system/logs', { params });
  },

  // 備份數據
  backup: (): Promise<void> => {
    const filename = `backup-${new Date().toISOString().split('T')[0]}.sql`;
    return apiClient.download('/system/backup', filename);
  },

  // 健康檢查
  healthCheck: (): Promise<ApiResponse<{ status: string; timestamp: string }>> => {
    return apiClient.get('/system/health', {
      skipErrorHandler: true,
    });
  },
};

// 導出所有 API
export default {
  auth: authAPI,
  quotation: quotationAPI,
  customer: customerAPI,
  product: productAPI,
  invoice: invoiceAPI,
  deliveryNote: deliveryNoteAPI,
  file: fileAPI,
  system: systemAPI,
};
