<template>
  <div class="product-form">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h3>{{ isEdit ? $t('product.editProduct') : $t('product.addProduct') }}</h3>
          <div class="header-actions">
            <el-button @click="goBack" :icon="ArrowLeft">
              {{ $t('common.back') }}
            </el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="default"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="24">
          <!-- 基本信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('product.basicInfo') }}</h4>
          </el-col>
          
          <el-col :span="12">
            <el-form-item :label="$t('product.category')" prop="categoryId">
              <el-select
                v-model="formData.categoryId"
                :placeholder="$t('product.selectCategory')"
                style="width: 100%"
              >
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :value="category.id"
                  :label="category.name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('product.status')" prop="status">
              <el-select
                v-model="formData.status"
                :placeholder="$t('product.selectStatus')"
                style="width: 100%"
              >
                <el-option
                  value="active"
                  :label="$t('status.active')"
                />
                <el-option
                  value="inactive"
                  :label="$t('status.inactive')"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('product.name')" prop="name">
              <el-input
                v-model="formData.name"
                :placeholder="$t('product.namePlaceholder')"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('product.description')" prop="description">
              <el-input
                v-model="formData.description"
                :placeholder="$t('product.descriptionPlaceholder')"
                type="textarea"
                :rows="3"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <!-- 價格和庫存 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('product.priceAndStock') }}</h4>
          </el-col>

          <el-col :span="8">
            <el-form-item :label="$t('product.unitPrice')" prop="unitPrice">
              <el-input-number
                v-model="formData.unitPrice"
                :placeholder="$t('product.unitPricePlaceholder')"
                :min="0"
                :max="1000000"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item :label="$t('product.stockQuantity')" prop="stockQuantity">
              <el-input-number
                v-model="formData.stockQuantity"
                :placeholder="$t('product.stockQuantityPlaceholder')"
                :min="0"
                :max="1000000"
                :precision="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item :label="$t('product.minStockLevel')" prop="minStockLevel">
              <el-input-number
                v-model="formData.minStockLevel"
                :placeholder="$t('product.minStockLevelPlaceholder')"
                :min="0"
                :max="1000"
                :precision="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('product.unit')" prop="unit">
              <el-select
                v-model="formData.unit"
                :placeholder="$t('product.selectUnit')"
                style="width: 100%"
                allow-create
                filterable
              >
                <el-option value="個" label="個" />
                <el-option value="套" label="套" />
                <el-option value="米" label="米" />
                <el-option value="公斤" label="公斤" />
                <el-option value="箱" label="箱" />
                <el-option value="包" label="包" />
                <el-option value="升" label="升" />
                <el-option value="噸" label="噸" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('product.supplier')" prop="supplier">
              <el-input
                v-model="formData.supplier"
                :placeholder="$t('product.supplierPlaceholder')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>

          <!-- 規格信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('product.specifications') }}</h4>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('product.barcode')" prop="barcode">
              <el-input
                v-model="formData.barcode"
                :placeholder="$t('product.barcodePlaceholder')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('product.weight')" prop="weight">
              <el-input-number
                v-model="formData.weight"
                :placeholder="$t('product.weightPlaceholder')"
                :min="0"
                :max="10000"
                :precision="2"
                style="width: 100%"
              >
                <template #append>公斤</template>
              </el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('product.dimensions')" prop="dimensions">
              <el-input
                v-model="formData.dimensions"
                :placeholder="$t('product.dimensionsPlaceholder')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('product.notes')" prop="notes">
              <el-input
                v-model="formData.notes"
                :placeholder="$t('product.notesPlaceholder')"
                type="textarea"
                :rows="4"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 表單操作按鈕 -->
        <div class="form-actions">
          <el-button @click="goBack">
            {{ $t('common.cancel') }}
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ $t('common.save') }}
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import { useProductStore } from '@/stores/product';
import { validationRules } from '@/utils';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const productStore = useProductStore();

// 表單引用
const formRef = ref(null);

// 狀態
const submitting = ref(false);
const isEdit = computed(() => !!route.params.id);

// 產品分類
const categories = computed(() => productStore.categories);

// 表單數據
const formData = reactive({
  categoryId: '',
  status: 'active',
  name: '',
  description: '',
  unitPrice: 0,
  stockQuantity: 0,
  minStockLevel: 10,
  unit: '個',
  supplier: '',
  barcode: '',
  weight: 0,
  dimensions: '',
  notes: ''
});

// 表單驗證規則
const formRules = computed(() => ({
  categoryId: [validationRules.required()],
  status: [validationRules.required()],
  name: [
    validationRules.required(),
    validationRules.minLength(2),
    validationRules.maxLength(100)
  ],
  description: [
    validationRules.required(),
    validationRules.maxLength(500)
  ],
  unitPrice: [
    validationRules.required(),
    validationRules.positiveNumber()
  ],
  stockQuantity: [
    validationRules.required(),
    {
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value < 0) {
          callback(new Error(t('product.stockQuantityError')));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  minStockLevel: [
    validationRules.required(),
    {
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || value < 0) {
          callback(new Error(t('product.minStockLevelError')));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  unit: [validationRules.required()],
  supplier: [validationRules.maxLength(100)],
  barcode: [validationRules.maxLength(50)],
  weight: [
    {
      validator: (rule, value, callback) => {
        if (value !== null && value !== undefined && value < 0) {
          callback(new Error(t('product.weightError')));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  dimensions: [validationRules.maxLength(100)],
  notes: [validationRules.maxLength(1000)]
}));

// 返回列表頁
const goBack = () => {
  router.push('/products');
};

// 提交表單
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    let result;
    if (isEdit.value) {
      result = await productStore.updateProduct(route.params.id, formData);
    } else {
      result = await productStore.createProduct(formData);
    }

    if (result.success) {
      ElMessage.success(
        isEdit.value ? t('product.updateSuccess') : t('product.createSuccess')
      );
      goBack();
    } else {
      ElMessage.error(result.error || t('common.operationFailed'));
    }
  } catch (error) {
    console.error('Form validation failed:', error);
  } finally {
    submitting.value = false;
  }
};

// 加載產品數據（編輯模式）
const loadProductData = async () => {
  if (isEdit.value) {
    const result = await productStore.getProduct(route.params.id);
    if (result.success && result.data) {
      Object.assign(formData, result.data);
    } else {
      ElMessage.error(t('product.loadFailed'));
      goBack();
    }
  }
};

onMounted(() => {
  loadProductData();
});
</script>

<style scoped>
.product-form {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.section-title {
  color: #409EFF;
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.form-actions .el-button {
  min-width: 120px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .product-form {
    padding: 12px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style> 