# 蒼藍工程公司管理系統 - 部署指南

## 系統概述

**蒼藍工程公司管理系統** (H.B Engineering Company Management System) 是一個現代化的企業管理平台，專為香港工程公司設計。系統提供完整的報價、發票、送貨單、客戶和產品管理功能，支援桌面應用程式和網頁版本。

### 技術架構
- **前端**: Vue 3 + Element Plus + Electron
- **後端**: Python Flask (運行於 Raspberry Pi)
- **資料庫**: SQLite/PostgreSQL
- **部署**: 跨平台桌面應用 + 網頁版

## 系統要求

### 最低系統要求
- **作業系統**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **處理器**: Intel Core i3 或同等級 AMD 處理器
- **記憶體**: 4GB RAM
- **硬碟空間**: 500MB 可用空間
- **網路**: 寬頻網路連接

### 推薦系統要求
- **作業系統**: Windows 11, macOS 12+, Ubuntu 20.04+
- **處理器**: Intel Core i5 或同等級 AMD 處理器
- **記憶體**: 8GB RAM 或以上
- **硬碟空間**: 2GB 可用空間
- **網路**: 高速寬頻網路連接

### 開發環境要求
- **Node.js**: 18.x 或以上版本
- **npm**: 9.x 或以上版本
- **Git**: 最新版本
- **Python**: 3.8+ (後端開發)

## 安裝指南

### 1. 開發環境安裝

#### 步驟 1: 安裝 Node.js
```bash
# 下載並安裝 Node.js 18.x LTS
# 訪問 https://nodejs.org/ 下載安裝包

# 驗證安裝
node --version  # 應顯示 v18.x.x
npm --version   # 應顯示 9.x.x
```

#### 步驟 2: 克隆專案
```bash
# 克隆專案到本地
git clone [repository-url]
cd HBEngineeringFrontend

# 或者下載 ZIP 文件並解壓
```

#### 步驟 3: 安裝依賴
```bash
# 安裝所有 npm 依賴
npm install

# 如果遇到權限問題 (Windows)
npm install --force

# 如果遇到網路問題，可使用淘寶鏡像
npm install --registry https://registry.npmmirror.com
```

#### 步驟 4: 啟動開發服務器
```bash
# 啟動網頁版開發服務器
npm run dev:vite

# 啟動 Electron 桌面版
npm run dev:electron

# 運行測試
npm run test
```

### 2. 生產環境部署

#### 網頁版部署
```bash
# 構建生產版本
npm run build

# 構建文件將生成在 dist/ 目錄
# 將 dist/ 目錄內容部署到網頁服務器
```

#### 桌面應用程式打包
```bash
# Windows 版本
npm run build:win

# macOS 版本 (需要在 macOS 系統上執行)
npm run build:mac

# Linux 版本
npm run build:linux

# 打包文件將生成在 dist-electron/ 目錄
```

## 配置說明

### 1. 環境變數配置

創建 `.env` 文件：
```env
# API 基礎 URL
VITE_API_BASE_URL=http://localhost:5000/api

# 應用程式標題
VITE_APP_TITLE=蒼藍工程公司管理系統

# 開發模式
VITE_NODE_ENV=development

# 語言設定
VITE_DEFAULT_LOCALE=zh-HK

# 主題設定
VITE_DEFAULT_THEME=light
```

### 2. Vite 配置 (vite.config.js)
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/renderer')
    }
  },
  server: {
    port: 3000,
    host: '0.0.0.0'
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser'
  }
})
```

### 3. Electron 配置 (main.js)
```javascript
const { app, BrowserWindow } = require('electron')
const path = require('path')

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  })

  // 載入應用程式
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:3000')
  } else {
    mainWindow.loadFile('dist/index.html')
  }
}
```

## 功能模組說明

### 1. 用戶認證模組
- **登入頁面**: `/login`
- **功能**: 用戶登入、記住密碼、會話管理
- **權限**: 路由守衛保護所有頁面

### 2. 儀表板模組
- **主頁**: `/`
- **功能**: 業務統計、圖表展示、快速導航
- **組件**: StatCard, ChartCard, 數據表格

### 3. 報價單管理
- **列表頁**: `/quotations`
- **新增頁**: `/quotations/create`
- **編輯頁**: `/quotations/edit/:id`
- **功能**: CRUD 操作、狀態管理、PDF 生成

### 4. 發票管理
- **列表頁**: `/invoices`
- **新增頁**: `/invoices/create`
- **編輯頁**: `/invoices/edit/:id`
- **功能**: 從報價單生成、付款追蹤

### 5. 送貨單管理
- **列表頁**: `/delivery-notes`
- **新增頁**: `/delivery-notes/create`
- **編輯頁**: `/delivery-notes/edit/:id`
- **功能**: 送貨追蹤、簽收管理

### 6. 客戶管理
- **列表頁**: `/customers`
- **新增頁**: `/customers/create`
- **編輯頁**: `/customers/edit/:id`
- **功能**: 企業/個人客戶、信用管理

### 7. 產品管理
- **列表頁**: `/products`
- **新增頁**: `/products/create`
- **編輯頁**: `/products/edit/:id`
- **功能**: 庫存管理、分類管理

## 數據庫設計

### 主要數據表
```sql
-- 用戶表
CREATE TABLE users (
  id INTEGER PRIMARY KEY,
  username VARCHAR(50) UNIQUE,
  password_hash VARCHAR(255),
  email VARCHAR(100),
  role VARCHAR(20),
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- 客戶表
CREATE TABLE customers (
  id INTEGER PRIMARY KEY,
  type VARCHAR(20), -- 'corporate' or 'individual'
  name VARCHAR(100),
  email VARCHAR(100),
  phone VARCHAR(20),
  address TEXT,
  contact_person VARCHAR(50),
  tax_number VARCHAR(20),
  credit_limit DECIMAL(10,2),
  payment_terms VARCHAR(20),
  status VARCHAR(20),
  notes TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- 產品表
CREATE TABLE products (
  id INTEGER PRIMARY KEY,
  category_id INTEGER,
  name VARCHAR(100),
  description TEXT,
  unit_price DECIMAL(10,2),
  stock_quantity INTEGER,
  min_stock_level INTEGER,
  unit VARCHAR(20),
  supplier VARCHAR(100),
  barcode VARCHAR(50),
  weight DECIMAL(8,2),
  dimensions VARCHAR(100),
  status VARCHAR(20),
  notes TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- 報價單表
CREATE TABLE quotations (
  id INTEGER PRIMARY KEY,
  quotation_number VARCHAR(50) UNIQUE,
  customer_id INTEGER,
  customer_name VARCHAR(100),
  customer_email VARCHAR(100),
  customer_phone VARCHAR(20),
  customer_address TEXT,
  valid_until DATE,
  subtotal DECIMAL(10,2),
  discount DECIMAL(10,2),
  tax_amount DECIMAL(10,2),
  total_amount DECIMAL(10,2),
  status VARCHAR(20),
  notes TEXT,
  terms TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- 報價單項目表
CREATE TABLE quotation_items (
  id INTEGER PRIMARY KEY,
  quotation_id INTEGER,
  product_id INTEGER,
  product_name VARCHAR(100),
  description TEXT,
  quantity INTEGER,
  unit_price DECIMAL(10,2),
  total_price DECIMAL(10,2),
  FOREIGN KEY (quotation_id) REFERENCES quotations(id),
  FOREIGN KEY (product_id) REFERENCES products(id)
);
```

## API 接口設計

### 認證接口
```
POST /api/auth/login
POST /api/auth/logout
GET  /api/auth/profile
```

### 客戶管理接口
```
GET    /api/customers          # 獲取客戶列表
POST   /api/customers          # 創建客戶
GET    /api/customers/:id      # 獲取客戶詳情
PUT    /api/customers/:id      # 更新客戶
DELETE /api/customers/:id      # 刪除客戶
```

### 產品管理接口
```
GET    /api/products           # 獲取產品列表
POST   /api/products           # 創建產品
GET    /api/products/:id       # 獲取產品詳情
PUT    /api/products/:id       # 更新產品
DELETE /api/products/:id       # 刪除產品
GET    /api/products/categories # 獲取產品分類
```

### 報價單管理接口
```
GET    /api/quotations         # 獲取報價單列表
POST   /api/quotations         # 創建報價單
GET    /api/quotations/:id     # 獲取報價單詳情
PUT    /api/quotations/:id     # 更新報價單
DELETE /api/quotations/:id     # 刪除報價單
POST   /api/quotations/:id/approve # 批准報價單
POST   /api/quotations/:id/reject  # 拒絕報價單
```

## 故障排除

### 常見問題

#### 1. 安裝依賴失敗
```bash
# 清除 npm 緩存
npm cache clean --force

# 刪除 node_modules 重新安裝
rm -rf node_modules package-lock.json
npm install
```

#### 2. 啟動失敗
```bash
# 檢查 Node.js 版本
node --version

# 檢查端口是否被佔用
netstat -an | grep 3000

# 使用不同端口啟動
npm run dev:vite -- --port 3001
```

#### 3. 構建失敗
```bash
# 檢查磁盤空間
df -h

# 增加 Node.js 記憶體限制
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

#### 4. Electron 打包失敗
```bash
# 安裝 Electron 預構建版本
npm install electron --save-dev

# 清除 Electron 緩存
npx electron-builder install-app-deps
```

### 日誌和調試

#### 開發模式日誌
```bash
# 啟動時顯示詳細日誌
DEBUG=* npm run dev:vite

# 查看 Electron 日誌
npm run dev:electron --verbose
```

#### 生產模式日誌
```javascript
// 在 main.js 中添加日誌
console.log('Application starting...')

// 使用 winston 記錄日誌
const winston = require('winston')
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'app.log' })
  ]
})
```

## 性能優化

### 1. 前端優化
```javascript
// 路由懶加載
const Dashboard = () => import('../views/Dashboard.vue')

// 組件懶加載
const DataTable = defineAsyncComponent(() => 
  import('../components/DataTable.vue')
)

// 圖片懶加載
<img v-lazy="imageUrl" alt="description">
```

### 2. 構建優化
```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus']
        }
      }
    }
  }
})
```

### 3. 運行時優化
```javascript
// 使用 keep-alive 緩存組件
<keep-alive>
  <router-view />
</keep-alive>

// 虛擬滾動大列表
<virtual-list :items="largeList" />
```

## 安全配置

### 1. CSP 設定
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; script-src 'self' 'unsafe-inline';">
```

### 2. Electron 安全
```javascript
// 禁用 Node.js 集成
webPreferences: {
  nodeIntegration: false,
  contextIsolation: true,
  enableRemoteModule: false
}
```

### 3. API 安全
```javascript
// JWT Token 驗證
const token = localStorage.getItem('token')
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
```

## 備份和恢復

### 1. 數據備份
```bash
# 備份 SQLite 數據庫
cp database.db database_backup_$(date +%Y%m%d).db

# 備份配置文件
tar -czf config_backup.tar.gz config/
```

### 2. 應用程式備份
```bash
# 備份整個應用程式
tar -czf app_backup_$(date +%Y%m%d).tar.gz HBEngineeringFrontend/
```

## 監控和維護

### 1. 系統監控
```bash
# 檢查應用程式狀態
ps aux | grep electron

# 檢查記憶體使用
top -p $(pgrep electron)

# 檢查磁盤使用
du -sh HBEngineeringFrontend/
```

### 2. 定期維護
- 每週檢查系統日誌
- 每月更新依賴包
- 每季度備份數據
- 每年檢查安全設定

## 支援和聯繫

### 技術支援
- **文檔**: 查看 README.md 和 PROJECT_SUMMARY.md
- **問題報告**: 通過 GitHub Issues 提交
- **功能請求**: 通過 GitHub Discussions 討論

### 培訓資源
- **用戶手冊**: docs/user-manual.pdf
- **管理員指南**: docs/admin-guide.pdf
- **開發者文檔**: docs/developer-guide.md
- **視頻教程**: 待製作

---

**版本**: 1.0.0  
**更新日期**: 2024年6月  
**維護團隊**: AI Assistant  
**聯繫方式**: [<EMAIL>] 