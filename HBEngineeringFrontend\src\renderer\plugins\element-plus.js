import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

// 配置 Element Plus
export function setupElementPlus(app) {
  app.use(ElementPlus, {
    locale: zhCn,
    // 在開發環境中抑制棄用警告
    ...(import.meta.env.DEV && {
      // 抑制 Element Plus 的棄用警告
      namespace: 'el',
      zIndex: 3000,
    })
  });

  // 在開發環境中抑制特定的棄用警告
  if (import.meta.env.DEV) {
    // 重寫 console.warn 來過濾 Element Plus 的棄用警告
    const originalWarn = console.warn;
    console.warn = (...args) => {
      const message = args.join(' ');
      
      // 過濾掉 Element Plus 的棄用警告
      if (
        message.includes('ElementPlusError') ||
        message.includes('type.text is about to be deprecated') ||
        message.includes('label act as value is about to be deprecated')
      ) {
        return; // 不顯示這些警告
      }
      
      // 其他警告正常顯示
      originalWarn.apply(console, args);
    };
  }
}

export default ElementPlus;
