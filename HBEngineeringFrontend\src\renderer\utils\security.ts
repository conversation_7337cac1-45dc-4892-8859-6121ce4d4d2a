// 安全工具函數

import CryptoJS from 'crypto-js';

// 加密密鑰（實際應用中應該從環境變量獲取）
const ENCRYPTION_KEY = import.meta.env.VITE_ENCRYPTION_KEY || 'hb-engineering-default-key';

/**
 * XSS 防護 - HTML 轉義
 */
export function escapeHtml(text: string): string {
  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;',
    '/': '&#x2F;',
    '`': '&#x60;',
    '=': '&#x3D;'
  };
  
  return text.replace(/[&<>"'`=\/]/g, (s) => map[s]);
}

/**
 * 反轉義 HTML
 */
export function unescapeHtml(text: string): string {
  const map: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#039;': "'",
    '&#x2F;': '/',
    '&#x60;': '`',
    '&#x3D;': '='
  };
  
  return text.replace(/&amp;|&lt;|&gt;|&quot;|&#039;|&#x2F;|&#x60;|&#x3D;/g, (s) => map[s]);
}

/**
 * 清理 HTML 標籤
 */
export function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '');
}

/**
 * 驗證 URL 是否安全
 */
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    // 只允許 http 和 https 協議
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
}

/**
 * 驗證是否為安全的重定向 URL
 */
export function isSafeRedirectUrl(url: string, allowedDomains: string[] = []): boolean {
  if (!isValidUrl(url)) {
    return false;
  }
  
  try {
    const urlObj = new URL(url);
    const currentDomain = window.location.hostname;
    
    // 允許同域重定向
    if (urlObj.hostname === currentDomain) {
      return true;
    }
    
    // 檢查是否在允許的域名列表中
    return allowedDomains.includes(urlObj.hostname);
  } catch {
    return false;
  }
}

/**
 * 生成隨機字符串
 */
export function generateRandomString(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成 UUID
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 密碼強度檢查
 */
export interface PasswordStrength {
  score: number; // 0-4
  feedback: string[];
  isValid: boolean;
}

export function checkPasswordStrength(password: string): PasswordStrength {
  const feedback: string[] = [];
  let score = 0;
  
  // 長度檢查
  if (password.length < 8) {
    feedback.push('密碼長度至少需要8個字符');
  } else if (password.length >= 12) {
    score += 1;
  }
  
  // 包含小寫字母
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('密碼應包含小寫字母');
  }
  
  // 包含大寫字母
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('密碼應包含大寫字母');
  }
  
  // 包含數字
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('密碼應包含數字');
  }
  
  // 包含特殊字符
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1;
  } else {
    feedback.push('密碼應包含特殊字符');
  }
  
  // 常見密碼檢查
  const commonPasswords = ['123456', 'password', '123456789', '12345678', '12345', '1234567', 'admin', 'qwerty'];
  if (commonPasswords.includes(password.toLowerCase())) {
    score = 0;
    feedback.push('請避免使用常見密碼');
  }
  
  return {
    score: Math.min(score, 4),
    feedback,
    isValid: score >= 3 && password.length >= 8
  };
}

/**
 * 加密敏感數據
 */
export function encryptData(data: string): string {
  try {
    return CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
  } catch (error) {
    console.error('Encryption failed:', error);
    return data;
  }
}

/**
 * 解密敏感數據
 */
export function decryptData(encryptedData: string): string {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('Decryption failed:', error);
    return encryptedData;
  }
}

/**
 * 生成哈希值
 */
export function generateHash(data: string): string {
  return CryptoJS.SHA256(data).toString();
}

/**
 * 驗證文件類型
 */
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

/**
 * 驗證文件大小
 */
export function validateFileSize(file: File, maxSizeInMB: number): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return file.size <= maxSizeInBytes;
}

/**
 * 安全的 JSON 解析
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString);
  } catch {
    return defaultValue;
  }
}

/**
 * 防止 CSRF 攻擊的 token 生成
 */
export function generateCSRFToken(): string {
  return generateRandomString(32);
}

/**
 * 驗證 CSRF token
 */
export function validateCSRFToken(token: string, expectedToken: string): boolean {
  return token === expectedToken;
}

/**
 * 輸入驗證器
 */
export class InputValidator {
  // 驗證郵箱
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  // 驗證手機號
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }
  
  // 驗證身份證號
  static isValidIdCard(idCard: string): boolean {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idCardRegex.test(idCard);
  }
  
  // 驗證銀行卡號
  static isValidBankCard(cardNumber: string): boolean {
    const cardRegex = /^\d{16,19}$/;
    return cardRegex.test(cardNumber);
  }
  
  // 驗證 IP 地址
  static isValidIP(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  }
  
  // 驗證數字範圍
  static isInRange(value: number, min: number, max: number): boolean {
    return value >= min && value <= max;
  }
  
  // 驗證字符串長度
  static isValidLength(str: string, min: number, max: number): boolean {
    return str.length >= min && str.length <= max;
  }
  
  // 驗證是否只包含字母和數字
  static isAlphanumeric(str: string): boolean {
    const alphanumericRegex = /^[a-zA-Z0-9]+$/;
    return alphanumericRegex.test(str);
  }
  
  // 驗證是否為正整數
  static isPositiveInteger(value: string | number): boolean {
    const num = typeof value === 'string' ? parseInt(value, 10) : value;
    return Number.isInteger(num) && num > 0;
  }
}

/**
 * 安全的本地存儲
 */
export class SecureStorage {
  private static encrypt = true;
  
  static setItem(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value);
      const finalValue = this.encrypt ? encryptData(serializedValue) : serializedValue;
      localStorage.setItem(key, finalValue);
    } catch (error) {
      console.error('Failed to set item in secure storage:', error);
    }
  }
  
  static getItem<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      if (!item) return defaultValue;
      
      const decryptedValue = this.encrypt ? decryptData(item) : item;
      return JSON.parse(decryptedValue);
    } catch (error) {
      console.error('Failed to get item from secure storage:', error);
      return defaultValue;
    }
  }
  
  static removeItem(key: string): void {
    localStorage.removeItem(key);
  }
  
  static clear(): void {
    localStorage.clear();
  }
}

/**
 * 內容安全策略 (CSP) 工具
 */
export class CSPHelper {
  // 生成 nonce
  static generateNonce(): string {
    return generateRandomString(16);
  }
  
  // 驗證內聯腳本
  static isInlineScriptAllowed(nonce: string): boolean {
    // 檢查當前頁面的 CSP 設置
    const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
    for (const meta of metaTags) {
      const content = meta.getAttribute('content');
      if (content && content.includes(`'nonce-${nonce}'`)) {
        return true;
      }
    }
    return false;
  }
}

/**
 * 速率限制器
 */
export class RateLimiter {
  private static requests = new Map<string, number[]>();
  
  static isAllowed(key: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!this.requests.has(key)) {
      this.requests.set(key, []);
    }
    
    const keyRequests = this.requests.get(key)!;
    
    // 清理過期的請求記錄
    const validRequests = keyRequests.filter(timestamp => timestamp > windowStart);
    
    if (validRequests.length >= maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }
  
  static reset(key: string): void {
    this.requests.delete(key);
  }
  
  static resetAll(): void {
    this.requests.clear();
  }
}
