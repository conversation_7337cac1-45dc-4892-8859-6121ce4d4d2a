const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// 開發模式檢測
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

function createWindow() {
  // 創建瀏覽器窗口
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: false // 開發模式下允許跨域
    },
    icon: path.join(__dirname, 'assets/icon.png'), // 可選圖標
    show: false // 先隱藏窗口，等加載完成後顯示
  });

  // 加載應用
  if (isDev) {
    // 開發模式：連接到Vite開發服務器
    mainWindow.loadURL('http://localhost:3000');
    // 打開開發者工具
    mainWindow.webContents.openDevTools();
  } else {
    // 生產模式：加載構建後的文件
    mainWindow.loadFile(path.join(__dirname, 'dist/index.html'));
  }

  // 窗口準備好後顯示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 處理窗口關閉
  mainWindow.on('closed', () => {
    // 在macOS上，應用通常會保持活動狀態
    if (process.platform !== 'darwin') {
      app.quit();
    }
  });
}

// 當Electron完成初始化並準備創建瀏覽器窗口時調用此方法
app.whenReady().then(createWindow);

// 當所有窗口都關閉時退出應用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 在macOS上，當點擊dock圖標且沒有其他窗口打開時，重新創建窗口
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 安全設置：防止新窗口創建
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
  });
});

// 處理應用協議（可選）
app.setAsDefaultProtocolClient('hb-engineering');

// IPC 通信處理
ipcMain.handle('get-api-url', () => {
  return process.env.API_URL || 'http://localhost:5000/api';
}); 