/* 全局樣式 */
:root {
    --primary-color: #409EFF;
    --success-color: #67C23A;
    --warning-color: #E6A23C;
    --danger-color: #F56C6C;
    --info-color: #909399;
    --text-color: #303133;
    --text-color-secondary: #606266;
    --border-color: #DCDFE6;
    --background-color: #F5F7FA;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--background-color);
}

/* 布局容器 */
.layout-container {
    height: 100vh;
}

/* 側邊欄 */
.el-aside {
    background-color: #304156;
    transition: width 0.3s;
}

.logo {
    height: 60px;
    padding: 10px;
    text-align: center;
    background-color: #2b3649;
}

.logo img {
    height: 40px;
}

.el-menu-vertical {
    border-right: none;
}

/* 頂部導航 */
.el-header {
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 60px;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.user-info span {
    margin-left: 8px;
}

/* 主要內容區 */
.el-main {
    padding: 20px;
    background-color: var(--background-color);
}

/* 卡片樣式 */
.el-card {
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: #fff;
    overflow: hidden;
    color: var(--text-color);
    transition: 0.3s;
}

.el-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

/* 表格樣式 */
.el-table {
    width: 100%;
    margin-bottom: 20px;
}

.el-table th {
    background-color: #f5f7fa;
    color: var(--text-color);
    font-weight: 500;
}

/* 表單樣式 */
.el-form-item {
    margin-bottom: 22px;
}

.el-form-item__label {
    font-weight: 500;
}

/* 按鈕樣式 */
.el-button {
    font-weight: 500;
}

.el-button--primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 響應式設計 */
@media screen and (max-width: 768px) {
    .el-aside {
        width: 64px !important;
    }
    
    .el-header {
        padding: 0 10px;
    }
    
    .el-main {
        padding: 10px;
    }
} 