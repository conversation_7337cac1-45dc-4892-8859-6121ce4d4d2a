<template>
  <div class="customer-form">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h3>{{ isEdit ? $t('customer.editCustomer') : $t('customer.addCustomer') }}</h3>
          <div class="header-actions">
            <el-button @click="goBack" :icon="ArrowLeft">
              {{ $t('common.back') }}
            </el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="default"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="24">
          <!-- 基本信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('customer.basicInfo') }}</h4>
          </el-col>
          
          <el-col :span="12">
            <el-form-item :label="$t('customer.type')" prop="type">
              <el-select
                v-model="formData.type"
                :placeholder="$t('customer.selectType')"
                style="width: 100%"
                @change="handleTypeChange"
              >
                <el-option
                  value="corporate"
                  :label="$t('customer.corporate')"
                />
                <el-option
                  value="individual"
                  :label="$t('customer.individual')"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.status')" prop="status">
              <el-select
                v-model="formData.status"
                :placeholder="$t('customer.selectStatus')"
                style="width: 100%"
              >
                <el-option
                  value="active"
                  :label="$t('status.active')"
                />
                <el-option
                  value="inactive"
                  :label="$t('status.inactive')"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('customer.name')" prop="name">
              <el-input
                v-model="formData.name"
                :placeholder="formData.type === 'corporate' ? $t('customer.companyNamePlaceholder') : $t('customer.individualNamePlaceholder')"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <!-- 聯絡信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('customer.contactInfo') }}</h4>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.email')" prop="email">
              <el-input
                v-model="formData.email"
                :placeholder="$t('customer.emailPlaceholder')"
                type="email"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.phone')" prop="phone">
              <el-input
                v-model="formData.phone"
                :placeholder="$t('customer.phonePlaceholder')"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('customer.address')" prop="address">
              <el-input
                v-model="formData.address"
                :placeholder="$t('customer.addressPlaceholder')"
                type="textarea"
                :rows="3"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <!-- 企業客戶專用字段 -->
          <template v-if="formData.type === 'corporate'">
            <el-col :span="24">
              <h4 class="section-title">{{ $t('customer.corporateInfo') }}</h4>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('customer.contactPerson')" prop="contactPerson">
                <el-input
                  v-model="formData.contactPerson"
                  :placeholder="$t('customer.contactPersonPlaceholder')"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('customer.taxNumber')" prop="taxNumber">
                <el-input
                  v-model="formData.taxNumber"
                  :placeholder="$t('customer.taxNumberPlaceholder')"
                />
              </el-form-item>
            </el-col>
          </template>

          <!-- 業務信息 -->
          <el-col :span="24">
            <h4 class="section-title">{{ $t('customer.businessInfo') }}</h4>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.creditLimit')" prop="creditLimit">
              <el-input-number
                v-model="formData.creditLimit"
                :placeholder="$t('customer.creditLimitPlaceholder')"
                :min="0"
                :max="10000000"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('customer.paymentTerms')" prop="paymentTerms">
              <el-select
                v-model="formData.paymentTerms"
                :placeholder="$t('customer.selectPaymentTerms')"
                style="width: 100%"
              >
                <el-option value="COD" label="COD (現金交付)" />
                <el-option value="NET15" label="NET15 (15天內付款)" />
                <el-option value="NET30" label="NET30 (30天內付款)" />
                <el-option value="NET60" label="NET60 (60天內付款)" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item :label="$t('customer.notes')" prop="notes">
              <el-input
                v-model="formData.notes"
                :placeholder="$t('customer.notesPlaceholder')"
                type="textarea"
                :rows="4"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 表單操作按鈕 -->
        <div class="form-actions">
          <el-button @click="goBack">
            {{ $t('common.cancel') }}
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ $t('common.save') }}
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import { useCustomerStore } from '@/stores/customer';
import { validationRules } from '@/utils';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const customerStore = useCustomerStore();

// 表單引用
const formRef = ref(null);

// 狀態
const submitting = ref(false);
const isEdit = computed(() => !!route.params.id);

// 表單數據
const formData = reactive({
  type: 'corporate',
  status: 'active',
  name: '',
  email: '',
  phone: '',
  address: '',
  contactPerson: '',
  taxNumber: '',
  creditLimit: 100000,
  paymentTerms: 'NET30',
  notes: ''
});

// 表單驗證規則
const formRules = computed(() => ({
  type: [validationRules.required()],
  status: [validationRules.required()],
  name: [
    validationRules.required(),
    validationRules.minLength(2),
    validationRules.maxLength(100)
  ],
  email: [
    validationRules.required(),
    validationRules.email()
  ],
  phone: [
    validationRules.required(),
    validationRules.phone()
  ],
  address: [
    validationRules.required(),
    validationRules.maxLength(500)
  ],
  contactPerson: formData.type === 'corporate' ? [
    validationRules.required(),
    validationRules.maxLength(50)
  ] : [],
  taxNumber: formData.type === 'corporate' ? [
    validationRules.required(),
    validationRules.minLength(8),
    validationRules.maxLength(20)
  ] : [],
  creditLimit: [
    validationRules.required(),
    validationRules.positiveNumber()
  ],
  paymentTerms: [validationRules.required()],
  notes: [validationRules.maxLength(1000)]
}));

// 處理客戶類型變更
const handleTypeChange = (type) => {
  if (type === 'individual') {
    formData.contactPerson = '';
    formData.taxNumber = '';
  }
};

// 返回列表頁
const goBack = () => {
  router.push('/customers');
};

// 提交表單
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    let result;
    if (isEdit.value) {
      result = await customerStore.updateCustomer(route.params.id, formData);
    } else {
      result = await customerStore.createCustomer(formData);
    }

    if (result.success) {
      ElMessage.success(
        isEdit.value ? t('customer.updateSuccess') : t('customer.createSuccess')
      );
      goBack();
    } else {
      ElMessage.error(result.error || t('common.operationFailed'));
    }
  } catch (error) {
    console.error('Form validation failed:', error);
  } finally {
    submitting.value = false;
  }
};

// 加載客戶數據（編輯模式）
const loadCustomerData = async () => {
  if (isEdit.value) {
    const result = await customerStore.getCustomer(route.params.id);
    if (result.success && result.data) {
      Object.assign(formData, result.data);
    } else {
      ElMessage.error(t('customer.loadFailed'));
      goBack();
    }
  }
};

onMounted(() => {
  loadCustomerData();
});
</script>

<style scoped>
.customer-form {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.section-title {
  color: #409EFF;
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.form-actions .el-button {
  min-width: 120px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .customer-form {
    padding: 12px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style> 