{"version": 3, "sources": ["../../element-plus/dist/locale/zh-hk.mjs"], "sourcesContent": ["/*! Element Plus v2.10.1 */\n\nvar zhHk = {\n  name: \"zh-hk\",\n  el: {\n    breadcrumb: {\n      label: \"\\u9EB5\\u5305\\u5C51\"\n    },\n    colorpicker: {\n      confirm: \"\\u78BA\\u8A8D\",\n      clear: \"\\u6E05\\u7A7A\",\n      defaultLabel: \"\\u984F\\u8272\\u9078\\u64C7\\u5668\",\n      description: \"\\u7576\\u524D\\u984F\\u8272\\u70BA {color}\\u3002\\u6309 Enter \\u9375\\u9078\\u64C7\\u65B0\\u984F\\u8272\\u3002\",\n      alphaLabel: \"\\u9078\\u64C7\\u900F\\u660E\\u5EA6\\u7684\\u503C\"\n    },\n    datepicker: {\n      now: \"\\u73FE\\u5728\",\n      today: \"\\u4ECA\\u5929\",\n      cancel: \"\\u53D6\\u6D88\",\n      clear: \"\\u6E05\\u7A7A\",\n      confirm: \"\\u78BA\\u8A8D\",\n      dateTablePrompt: \"\\u4F7F\\u7528\\u65B9\\u5411\\u9375\\u8207 Enter \\u9375\\u4EE5\\u9078\\u64C7\\u65E5\\u671F\",\n      monthTablePrompt: \"\\u4F7F\\u7528\\u65B9\\u5411\\u9375\\u8207 Enter \\u9375\\u4EE5\\u9078\\u64C7\\u6708\\u4EFD\",\n      yearTablePrompt: \"\\u4F7F\\u7528\\u65B9\\u5411\\u9375\\u8207 Enter \\u9375\\u4EE5\\u9078\\u64C7\\u5E74\\u4EFD\",\n      selectedDate: \"\\u5DF2\\u9078\\u65E5\\u671F\",\n      selectDate: \"\\u9078\\u64C7\\u65E5\\u671F\",\n      selectTime: \"\\u9078\\u64C7\\u6642\\u9593\",\n      startDate: \"\\u958B\\u59CB\\u65E5\\u671F\",\n      startTime: \"\\u958B\\u59CB\\u6642\\u9593\",\n      endDate: \"\\u7D50\\u675F\\u65E5\\u671F\",\n      endTime: \"\\u7D50\\u675F\\u6642\\u9593\",\n      prevYear: \"\\u524D\\u4E00\\u5E74\",\n      nextYear: \"\\u5F8C\\u4E00\\u5E74\",\n      prevMonth: \"\\u4E0A\\u500B\\u6708\",\n      nextMonth: \"\\u4E0B\\u500B\\u6708\",\n      year: \"\\u5E74\",\n      month1: \"1 \\u6708\",\n      month2: \"2 \\u6708\",\n      month3: \"3 \\u6708\",\n      month4: \"4 \\u6708\",\n      month5: \"5 \\u6708\",\n      month6: \"6 \\u6708\",\n      month7: \"7 \\u6708\",\n      month8: \"8 \\u6708\",\n      month9: \"9 \\u6708\",\n      month10: \"10 \\u6708\",\n      month11: \"11 \\u6708\",\n      month12: \"12 \\u6708\",\n      weeks: {\n        sun: \"\\u65E5\",\n        mon: \"\\u4E00\",\n        tue: \"\\u4E8C\",\n        wed: \"\\u4E09\",\n        thu: \"\\u56DB\",\n        fri: \"\\u4E94\",\n        sat: \"\\u516D\"\n      },\n      weeksFull: {\n        sun: \"\\u661F\\u671F\\u65E5\",\n        mon: \"\\u661F\\u671F\\u4E00\",\n        tue: \"\\u661F\\u671F\\u4E8C\",\n        wed: \"\\u661F\\u671F\\u4E09\",\n        thu: \"\\u661F\\u671F\\u56DB\",\n        fri: \"\\u661F\\u671F\\u4E94\",\n        sat: \"\\u661F\\u671F\\u516D\"\n      },\n      months: {\n        jan: \"\\u4E00\\u6708\",\n        feb: \"\\u4E8C\\u6708\",\n        mar: \"\\u4E09\\u6708\",\n        apr: \"\\u56DB\\u6708\",\n        may: \"\\u4E94\\u6708\",\n        jun: \"\\u516D\\u6708\",\n        jul: \"\\u4E03\\u6708\",\n        aug: \"\\u516B\\u6708\",\n        sep: \"\\u4E5D\\u6708\",\n        oct: \"\\u5341\\u6708\",\n        nov: \"\\u5341\\u4E00\\u6708\",\n        dec: \"\\u5341\\u4E8C\\u6708\"\n      }\n    },\n    inputNumber: {\n      decrease: \"\\u6E1B\\u5C11\\u6578\\u503C\",\n      increase: \"\\u589E\\u52A0\\u6578\\u503C\"\n    },\n    select: {\n      loading: \"\\u8F09\\u5165\\u4E2D\",\n      noMatch: \"\\u7121\\u5339\\u914D\\u8CC7\\u6599\",\n      noData: \"\\u7121\\u8CC7\\u6599\",\n      placeholder: \"\\u8ACB\\u9078\\u64C7\"\n    },\n    mention: {\n      loading: \"\\u8F09\\u5165\\u4E2D\"\n    },\n    dropdown: {\n      toggleDropdown: \"\\u5207\\u63DB\\u4E0B\\u62C9\\u9078\\u55AE\"\n    },\n    cascader: {\n      noMatch: \"\\u7121\\u5339\\u914D\\u8CC7\\u6599\",\n      loading: \"\\u8F09\\u5165\\u4E2D\",\n      placeholder: \"\\u8ACB\\u9078\\u64C7\",\n      noData: \"\\u7121\\u8CC7\\u6599\"\n    },\n    pagination: {\n      goto: \"\\u524D\\u5F80\",\n      pagesize: \"\\u9805/\\u9801\",\n      total: \"\\u5171 {total} \\u9805\",\n      pageClassifier: \"\\u9801\",\n      page: \"\\u9801\",\n      prev: \"\\u4E0A\\u4E00\\u9801\",\n      next: \"\\u4E0B\\u4E00\\u9801\",\n      currentPage: \"\\u7B2C {pager} \\u9801\",\n      prevPages: \"\\u5411\\u524D {pager} \\u9801\",\n      nextPages: \"\\u5411\\u5F8C {pager} \\u9801\",\n      deprecationWarning: \"\\u6AA2\\u6E2C\\u5230\\u5DF2\\u904E\\u6642\\u7684\\u4F7F\\u7528\\u65B9\\u5F0F\\uFF0C\\u8ACB\\u53C3\\u95B1 el-pagination \\u8AAA\\u660E\\u6587\\u4EF6\\u4EE5\\u4E86\\u89E3\\u66F4\\u591A\\u8CC7\\u8A0A\"\n    },\n    dialog: {\n      close: \"\\u95DC\\u9589\\u6B64\\u5C0D\\u8A71\\u6846\"\n    },\n    drawer: {\n      close: \"\\u95DC\\u9589\\u6B64\\u5C0D\\u8A71\\u6846\"\n    },\n    messagebox: {\n      title: \"\\u63D0\\u793A\",\n      confirm: \"\\u78BA\\u5B9A\",\n      cancel: \"\\u53D6\\u6D88\",\n      error: \"\\u8F38\\u5165\\u7684\\u8CC7\\u6599\\u4E0D\\u7B26\\u5408\\u898F\\u5B9A!\",\n      close: \"\\u95DC\\u9589\\u6B64\\u5C0D\\u8A71\\u6846\"\n    },\n    upload: {\n      deleteTip: \"\\u6309 Delete \\u9375\\u4EE5\\u522A\\u9664\",\n      delete: \"\\u522A\\u9664\",\n      preview: \"\\u67E5\\u770B\\u5716\\u7247\",\n      continue: \"\\u7E7C\\u7E8C\\u4E0A\\u50B3\"\n    },\n    slider: {\n      defaultLabel: \"\\u6ED1\\u687F\\u4ECB\\u65BC {min} \\u81F3 {max}\",\n      defaultRangeStartLabel: \"\\u9078\\u64C7\\u8D77\\u59CB\\u503C\",\n      defaultRangeEndLabel: \"\\u9078\\u64C7\\u7D50\\u675F\\u503C\"\n    },\n    table: {\n      emptyText: \"\\u66AB\\u7121\\u8CC7\\u6599\",\n      confirmFilter: \"\\u7BE9\\u9078\",\n      resetFilter: \"\\u91CD\\u7F6E\",\n      clearFilter: \"\\u5168\\u90E8\",\n      sumText: \"\\u5408\\u8A08\"\n    },\n    tour: {\n      next: \"\\u4E0B\\u4E00\\u6B65\",\n      previous: \"\\u4E0A\\u4E00\\u6B65\",\n      finish: \"\\u7D50\\u675F\\u5C0E\\u89BD\"\n    },\n    tree: {\n      emptyText: \"\\u66AB\\u7121\\u8CC7\\u6599\"\n    },\n    transfer: {\n      noMatch: \"\\u7121\\u5339\\u914D\\u8CC7\\u6599\",\n      noData: \"\\u7121\\u8CC7\\u6599\",\n      titles: [\"\\u5217\\u8868 1\", \"\\u5217\\u8868 2\"],\n      filterPlaceholder: \"\\u8ACB\\u8F38\\u5165\\u641C\\u5C0B\\u5167\\u5BB9\",\n      noCheckedFormat: \"\\u5171 {total} \\u9805\",\n      hasCheckedFormat: \"\\u5DF2\\u9078 {checked}/{total} \\u9805\"\n    },\n    image: {\n      error: \"\\u8F09\\u5165\\u5931\\u6557\"\n    },\n    pageHeader: {\n      title: \"\\u8FD4\\u56DE\"\n    },\n    popconfirm: {\n      confirmButtonText: \"\\u78BA\\u8A8D\",\n      cancelButtonText: \"\\u53D6\\u6D88\"\n    },\n    carousel: {\n      leftArrow: \"\\u4E0A\\u4E00\\u5F35\\u5E7B\\u71C8\\u7247\",\n      rightArrow: \"\\u4E0B\\u4E00\\u5F35\\u5E7B\\u71C8\\u7247\",\n      indicator: \"\\u5E7B\\u71C8\\u7247\\u5207\\u63DB\\u81F3\\u7D22\\u5F15 {index}\"\n    }\n  }\n};\n\nexport { zhHk as default };\n"], "mappings": ";;;AAEA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,IAAI;AAAA,IACF,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,cAAc;AAAA,MACd,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,WAAW;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,IACtB;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,cAAc;AAAA,MACd,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,WAAW;AAAA,IACb;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ,CAAC,QAAkB,MAAgB;AAAA,MAC3C,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACpB;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,EACF;AACF;", "names": []}