param(
    [int]$Port = 8080
)

# 設置目錄
$DistPath = Join-Path $PSScriptRoot "dist"

if (-not (Test-Path $DistPath)) {
    Write-Host "❌ 找不到 dist 目錄。請先運行 npm run build" -ForegroundColor Red
    exit 1
}

Write-Host "🚀 啟動蒼藍工程公司管理系統..." -ForegroundColor Green
Write-Host "📁 服務目錄: $DistPath" -ForegroundColor Cyan
Write-Host "🌐 訪問地址: http://localhost:$Port" -ForegroundColor Yellow
Write-Host "⏹️  按 Ctrl+C 停止服務器" -ForegroundColor Magenta

# 使用 .NET HttpListener 創建 Web 服務器
Add-Type -AssemblyName System.Net.Http

try {
    $listener = New-Object System.Net.HttpListener
    $listener.Prefixes.Add("http://localhost:$Port/")
    $listener.Start()
    
    Write-Host "✅ 服務器已啟動在端口 $Port" -ForegroundColor Green
    
    while ($listener.IsListening) {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response
        
        # 處理請求路徑
        $path = $request.Url.AbsolutePath
        if ($path -eq "/") {
            $path = "/index.html"
        }
        
        $filePath = Join-Path $DistPath $path.TrimStart('/')
        
        # 如果文件不存在，返回 index.html (SPA 路由)
        if (-not (Test-Path $filePath)) {
            $filePath = Join-Path $DistPath "index.html"
        }
        
        if (Test-Path $filePath) {
            $content = [System.IO.File]::ReadAllBytes($filePath)
            $response.ContentLength64 = $content.Length
            
            # 設置 Content-Type
            $extension = [System.IO.Path]::GetExtension($filePath)
            switch ($extension) {
                ".html" { $response.ContentType = "text/html; charset=utf-8" }
                ".js" { $response.ContentType = "application/javascript" }
                ".css" { $response.ContentType = "text/css" }
                ".json" { $response.ContentType = "application/json" }
                default { $response.ContentType = "application/octet-stream" }
            }
            
            $response.StatusCode = 200
            $response.OutputStream.Write($content, 0, $content.Length)
        } else {
            $response.StatusCode = 404
        }
        
        $response.Close()
        Write-Host "$(Get-Date -Format 'HH:mm:ss') $($request.HttpMethod) $($request.Url.AbsolutePath) - $($response.StatusCode)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 錯誤: $_" -ForegroundColor Red
} finally {
    if ($listener) {
        $listener.Stop()
        $listener.Close()
        Write-Host "⏹️  服務器已停止" -ForegroundColor Yellow
    }
} 