import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useCustomerStore = defineStore('customer', () => {
  // 狀態
  const customers = ref([]);
  const currentCustomer = ref(null);
  const loading = ref(false);
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0
  });
  
  // 搜索和篩選
  const searchQuery = ref('');
  const statusFilter = ref('');
  const typeFilter = ref('');

  // 計算屬性
  const filteredCustomers = computed(() => {
    let result = customers.value;
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(c => 
        c.name.toLowerCase().includes(query) ||
        c.code.toLowerCase().includes(query) ||
        c.email.toLowerCase().includes(query) ||
        c.phone.toLowerCase().includes(query)
      );
    }
    
    if (statusFilter.value) {
      result = result.filter(c => c.status === statusFilter.value);
    }
    
    if (typeFilter.value) {
      result = result.filter(c => c.type === typeFilter.value);
    }
    
    return result;
  });

  const totalPages = computed(() => {
    return Math.ceil(filteredCustomers.value.length / pagination.value.pageSize);
  });

  const paginatedCustomers = computed(() => {
    const start = (pagination.value.page - 1) * pagination.value.pageSize;
    const end = start + pagination.value.pageSize;
    return filteredCustomers.value.slice(start, end);
  });

  // 統計數據
  const statistics = computed(() => {
    const total = customers.value.length;
    const active = customers.value.filter(c => c.status === 'active').length;
    const inactive = customers.value.filter(c => c.status === 'inactive').length;
    const corporate = customers.value.filter(c => c.type === 'corporate').length;
    const individual = customers.value.filter(c => c.type === 'individual').length;
    
    return {
      total,
      active,
      inactive,
      corporate,
      individual
    };
  });

  // Actions
  const fetchCustomers = async (params = {}) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockData = generateMockCustomers(30);
      customers.value = mockData;
      pagination.value.total = mockData.length;
      
      return { success: true, data: mockData };
    } catch (error) {
      console.error('Failed to fetch customers:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const getCustomer = async (id) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const customer = customers.value.find(c => c.id === id) || 
        generateMockCustomer(id);
      
      currentCustomer.value = customer;
      return { success: true, data: customer };
    } catch (error) {
      console.error('Failed to fetch customer:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const createCustomer = async (customerData) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newCustomer = {
        ...customerData,
        id: Date.now().toString(),
        code: generateCustomerCode(),
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      customers.value.unshift(newCustomer);
      pagination.value.total++;
      
      return { success: true, data: newCustomer };
    } catch (error) {
      console.error('Failed to create customer:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const updateCustomer = async (id, customerData) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = customers.value.findIndex(c => c.id === id);
      if (index !== -1) {
        customers.value[index] = {
          ...customers.value[index],
          ...customerData,
          updatedAt: new Date().toISOString()
        };
        
        if (currentCustomer.value?.id === id) {
          currentCustomer.value = customers.value[index];
        }
      }
      
      return { success: true, data: customers.value[index] };
    } catch (error) {
      console.error('Failed to update customer:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const deleteCustomer = async (id) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = customers.value.findIndex(c => c.id === id);
      if (index !== -1) {
        customers.value.splice(index, 1);
        pagination.value.total--;
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to delete customer:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const updateStatus = async (id, status) => {
    return updateCustomer(id, { status });
  };

  // 重置狀態
  const reset = () => {
    customers.value = [];
    currentCustomer.value = null;
    loading.value = false;
    pagination.value = {
      page: 1,
      pageSize: 10,
      total: 0
    };
    searchQuery.value = '';
    statusFilter.value = '';
    typeFilter.value = '';
  };

  return {
    // 狀態
    customers: paginatedCustomers,
    allCustomers: customers,
    currentCustomer,
    loading,
    pagination,
    searchQuery,
    statusFilter,
    typeFilter,
    
    // 計算屬性
    filteredCustomers,
    totalPages,
    statistics,
    
    // Actions
    fetchCustomers,
    getCustomer,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    updateStatus,
    reset
  };
});

// 生成客戶編號
function generateCustomerCode() {
  const year = new Date().getFullYear();
  const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `CUS${year}${random}`;
}

// 生成模擬客戶數據
function generateMockCustomer(id = null) {
  const types = ['corporate', 'individual'];
  const statuses = ['active', 'inactive'];
  const companies = [
    '港島建築有限公司', '九龍工程集團', '新界發展公司', '大中華建設',
    '永安建築', '信和工程', '太古建設', '和記建築', '恆基工程'
  ];
  const districts = [
    '中環', '金鐘', '銅鑼灣', '灣仔', '尖沙咀', '旺角', '觀塘', '荃灣'
  ];
  
  const type = types[Math.floor(Math.random() * types.length)];
  const isCompany = type === 'corporate';
  
  return {
    id: id || Date.now().toString(),
    code: generateCustomerCode(),
    name: isCompany 
      ? companies[Math.floor(Math.random() * companies.length)]
      : `陳${['先生', '小姐', '太太'][Math.floor(Math.random() * 3)]}`,
    type,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    email: `customer${Math.floor(Math.random() * 1000)}@example.com`,
    phone: `+852 ${Math.floor(Math.random() * 9000) + 1000} ${Math.floor(Math.random() * 9000) + 1000}`,
    address: `香港${districts[Math.floor(Math.random() * districts.length)]}某街某號`,
    contactPerson: isCompany ? `聯絡人${Math.floor(Math.random() * 10) + 1}` : null,
    taxNumber: isCompany ? `${Math.floor(Math.random() * 90000000) + 10000000}` : null,
    creditLimit: Math.floor(Math.random() * 500000) + 50000,
    paymentTerms: ['NET30', 'NET15', 'COD'][Math.floor(Math.random() * 3)],
    notes: '客戶備註信息',
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  };
}

function generateMockCustomers(count) {
  return Array.from({ length: count }, () => generateMockCustomer());
} 