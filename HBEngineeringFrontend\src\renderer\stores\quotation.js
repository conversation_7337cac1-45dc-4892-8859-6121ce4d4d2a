import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { quotationAPI } from '../../api/simple-client.js';
import { ElMessage } from 'element-plus';

export const useQuotationStore = defineStore('quotation', () => {
  // 狀態
  const quotations = ref([]);
  const currentQuotation = ref(null);
  const loading = ref(false);
  const submitting = ref(false);
  const error = ref(null);
  
  const filters = ref({
    search: '',
    status: '',
    customer_id: '',
    date_from: '',
    date_to: '',
    amount_min: '',
    amount_max: '',
  });
  
  const pagination = ref({
    page: 1,
    size: 20,
    total: 0,
  });

  // 計算屬性
  const filteredQuotations = computed(() => {
    let result = quotations.value;
    
    // 搜索過濾
    if (filters.value.search) {
      const query = filters.value.search.toLowerCase();
      result = result.filter(q => 
        q.quotation_no?.toLowerCase().includes(query) ||
        q.customer_name?.toLowerCase().includes(query) ||
        q.project_name?.toLowerCase().includes(query) ||
        q.description?.toLowerCase().includes(query)
      );
    }
    
    // 狀態過濾
    if (filters.value.status) {
      result = result.filter(q => q.status === filters.value.status);
    }
    
    return result;
  });

  const totalPages = computed(() => {
    return Math.ceil(pagination.value.total / pagination.value.size);
  });

  // 統計數據
  const statistics = computed(() => {
    const all = quotations.value;
    
    return {
      total: all.length,
      draft: all.filter(q => q.status === 'draft').length,
      sent: all.filter(q => q.status === 'sent').length,
      accepted: all.filter(q => q.status === 'accepted').length,
      rejected: all.filter(q => q.status === 'rejected').length,
      expired: all.filter(q => q.status === 'expired').length,
      totalAmount: all.reduce((sum, q) => sum + (q.final_amount || 0), 0),
      acceptedAmount: all.filter(q => q.status === 'accepted').reduce((sum, q) => sum + (q.final_amount || 0), 0),
    };
  });

  // 狀態選項
  const statusOptions = computed(() => [
    { label: '草稿', value: 'draft', color: '#909399' },
    { label: '已發送', value: 'sent', color: '#E6A23C' },
    { label: '已接受', value: 'accepted', color: '#67C23A' },
    { label: '已拒絕', value: 'rejected', color: '#F56C6C' },
    { label: '已過期', value: 'expired', color: '#C0C4CC' },
  ]);

  // 獲取狀態標籤
  const getStatusLabel = (status) => {
    const option = statusOptions.value.find(opt => opt.value === status);
    return option?.label || status;
  };

  // 獲取狀態顏色
  const getStatusColor = (status) => {
    const option = statusOptions.value.find(opt => opt.value === status);
    return option?.color || '#909399';
  };

  // Actions
  const fetchQuotations = async (params = {}) => {
    try {
      loading.value = true;
      error.value = null;
      
      const response = await quotationAPI.getList(params);
      quotations.value = response.data?.items || response.data || [];
      pagination.value.total = response.data?.total || response.total || 0;
      
    } catch (err) {
      error.value = err.message || '獲取報價單列表失敗';
      ElMessage.error(error.value);
      quotations.value = [];
      pagination.value.total = 0;
    } finally {
      loading.value = false;
    }
  };

  const createQuotation = async (quotationData) => {
    try {
      submitting.value = true;
      error.value = null;
      
      const response = await quotationAPI.create(quotationData);
      const newQuotation = response.data;
      
      quotations.value.unshift(newQuotation);
      pagination.value.total++;
      
      ElMessage.success('報價單創建成功');
      return newQuotation;
      
    } catch (err) {
      error.value = err.message || '創建報價單失敗';
      ElMessage.error(error.value);
      return null;
    } finally {
      submitting.value = false;
    }
  };

  // 設置篩選條件
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters };
    pagination.value.page = 1;
  };

  // 重置篩選條件
  const resetFilters = () => {
    filters.value = {
      search: '',
      status: '',
      customer_id: '',
      date_from: '',
      date_to: '',
      amount_min: '',
      amount_max: '',
    };
    pagination.value.page = 1;
  };

  return {
    // 狀態
    quotations,
    currentQuotation,
    loading,
    submitting,
    error,
    filters,
    pagination,
    
    // 計算屬性
    filteredQuotations,
    totalPages,
    statistics,
    statusOptions,
    getStatusLabel,
    getStatusColor,
    
    // Actions
    fetchQuotations,
    createQuotation,
    setFilters,
    resetFilters,
  };
});
