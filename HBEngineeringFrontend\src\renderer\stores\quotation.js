import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useQuotationStore = defineStore('quotation', () => {
  // 狀態
  const quotations = ref([]);
  const currentQuotation = ref(null);
  const loading = ref(false);
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0
  });
  
  // 搜索和篩選
  const searchQuery = ref('');
  const statusFilter = ref('');
  const dateRange = ref([]);

  // 計算屬性
  const filteredQuotations = computed(() => {
    let result = quotations.value;
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(q => 
        q.quotationNumber.toLowerCase().includes(query) ||
        q.customerName.toLowerCase().includes(query) ||
        q.projectName.toLowerCase().includes(query)
      );
    }
    
    if (statusFilter.value) {
      result = result.filter(q => q.status === statusFilter.value);
    }
    
    if (dateRange.value && dateRange.value.length === 2) {
      const [start, end] = dateRange.value;
      result = result.filter(q => {
        const date = new Date(q.createdAt);
        return date >= start && date <= end;
      });
    }
    
    return result;
  });

  const totalPages = computed(() => {
    return Math.ceil(filteredQuotations.value.length / pagination.value.pageSize);
  });

  const paginatedQuotations = computed(() => {
    const start = (pagination.value.page - 1) * pagination.value.pageSize;
    const end = start + pagination.value.pageSize;
    return filteredQuotations.value.slice(start, end);
  });

  // 統計數據
  const statistics = computed(() => {
    const total = quotations.value.length;
    const draft = quotations.value.filter(q => q.status === 'draft').length;
    const pending = quotations.value.filter(q => q.status === 'pending').length;
    const approved = quotations.value.filter(q => q.status === 'approved').length;
    const rejected = quotations.value.filter(q => q.status === 'rejected').length;
    
    const totalAmount = quotations.value.reduce((sum, q) => sum + (q.totalAmount || 0), 0);
    const approvedAmount = quotations.value
      .filter(q => q.status === 'approved')
      .reduce((sum, q) => sum + (q.totalAmount || 0), 0);
    
    return {
      total,
      draft,
      pending,
      approved,
      rejected,
      totalAmount,
      approvedAmount
    };
  });

  // Actions
  const fetchQuotations = async (params = {}) => {
    loading.value = true;
    try {
      // 模擬 API 調用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 生成模擬數據
      const mockData = generateMockQuotations(50);
      quotations.value = mockData;
      pagination.value.total = mockData.length;
      
      return { success: true, data: mockData };
    } catch (error) {
      console.error('Failed to fetch quotations:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const getQuotation = async (id) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const quotation = quotations.value.find(q => q.id === id) || 
        generateMockQuotation(id);
      
      currentQuotation.value = quotation;
      return { success: true, data: quotation };
    } catch (error) {
      console.error('Failed to fetch quotation:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const createQuotation = async (quotationData) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newQuotation = {
        ...quotationData,
        id: Date.now().toString(),
        quotationNumber: generateQuotationNumber(),
        status: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      quotations.value.unshift(newQuotation);
      pagination.value.total++;
      
      return { success: true, data: newQuotation };
    } catch (error) {
      console.error('Failed to create quotation:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const updateQuotation = async (id, quotationData) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = quotations.value.findIndex(q => q.id === id);
      if (index !== -1) {
        quotations.value[index] = {
          ...quotations.value[index],
          ...quotationData,
          updatedAt: new Date().toISOString()
        };
        
        if (currentQuotation.value?.id === id) {
          currentQuotation.value = quotations.value[index];
        }
      }
      
      return { success: true, data: quotations.value[index] };
    } catch (error) {
      console.error('Failed to update quotation:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const deleteQuotation = async (id) => {
    loading.value = true;
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = quotations.value.findIndex(q => q.id === id);
      if (index !== -1) {
        quotations.value.splice(index, 1);
        pagination.value.total--;
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to delete quotation:', error);
      return { success: false, error };
    } finally {
      loading.value = false;
    }
  };

  const updateStatus = async (id, status) => {
    return updateQuotation(id, { status });
  };

  const duplicateQuotation = async (id) => {
    const original = quotations.value.find(q => q.id === id);
    if (!original) return { success: false, error: 'Quotation not found' };
    
    const duplicated = {
      ...original,
      id: undefined,
      quotationNumber: undefined,
      status: 'draft',
      createdAt: undefined,
      updatedAt: undefined
    };
    
    return createQuotation(duplicated);
  };

  // 重置狀態
  const reset = () => {
    quotations.value = [];
    currentQuotation.value = null;
    loading.value = false;
    pagination.value = {
      page: 1,
      pageSize: 10,
      total: 0
    };
    searchQuery.value = '';
    statusFilter.value = '';
    dateRange.value = [];
  };

  return {
    // 狀態
    quotations: paginatedQuotations,
    allQuotations: quotations,
    currentQuotation,
    loading,
    pagination,
    searchQuery,
    statusFilter,
    dateRange,
    
    // 計算屬性
    filteredQuotations,
    totalPages,
    statistics,
    
    // Actions
    fetchQuotations,
    getQuotation,
    createQuotation,
    updateQuotation,
    deleteQuotation,
    updateStatus,
    duplicateQuotation,
    reset
  };
});

// 生成報價單號碼
function generateQuotationNumber() {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');
  const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `QUO${year}${month}${random}`;
}

// 生成模擬報價單數據
function generateMockQuotation(id = null) {
  const statuses = ['draft', 'pending', 'approved', 'rejected'];
  const customers = ['ABC 公司', '大中華建築', '港島工程', '九龍建設', '新界發展'];
  const projects = ['商業大廈項目', '住宅樓宇建設', '基建工程', '裝修工程', '維修保養'];
  
  return {
    id: id || Date.now().toString(),
    quotationNumber: generateQuotationNumber(),
    customerName: customers[Math.floor(Math.random() * customers.length)],
    projectName: projects[Math.floor(Math.random() * projects.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    totalAmount: Math.floor(Math.random() * 1000000) + 10000,
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    items: [
      {
        id: '1',
        description: '工程材料',
        quantity: Math.floor(Math.random() * 100) + 1,
        unitPrice: Math.floor(Math.random() * 1000) + 100,
        amount: 0
      }
    ]
  };
}

function generateMockQuotations(count) {
  return Array.from({ length: count }, () => generateMockQuotation());
} 