import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { quotationAPI } from '@api/index';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import type { Quotation, QuotationItem, QuotationStatus, QuotationFilters } from '@/types/quotation';

export interface QuotationState {
  quotations: Quotation[];
  currentQuotation: Quotation | null;
  loading: boolean;
  submitting: boolean;
  filters: QuotationFilters;
  pagination: {
    page: number;
    size: number;
    total: number;
  };
}

export const useQuotationStore = defineStore('quotation', () => {
  // 狀態
  const quotations = ref<Quotation[]>([]);
  const currentQuotation = ref<Quotation | null>(null);
  const loading = ref(false);
  const submitting = ref(false);
  const error = ref<string | null>(null);

  const filters = ref<QuotationFilters>({
    search: '',
    status: '',
    customer_id: '',
    date_from: '',
    date_to: '',
    amount_min: '',
    amount_max: '',
  });

  const pagination = ref({
    page: 1,
    size: 20,
    total: 0,
  });

  // 計算屬性
  const filteredQuotations = computed(() => {
    let result = quotations.value;

    // 搜索過濾
    if (filters.value.search) {
      const query = filters.value.search.toLowerCase();
      result = result.filter(q =>
        q.quotation_no.toLowerCase().includes(query) ||
        q.customer_name.toLowerCase().includes(query) ||
        q.project_name?.toLowerCase().includes(query) ||
        q.description?.toLowerCase().includes(query)
      );
    }

    // 狀態過濾
    if (filters.value.status) {
      result = result.filter(q => q.status === filters.value.status);
    }

    // 客戶過濾
    if (filters.value.customer_id) {
      result = result.filter(q => q.customer_id.toString() === filters.value.customer_id);
    }

    // 日期範圍過濾
    if (filters.value.date_from) {
      result = result.filter(q => dayjs(q.created_at).isAfter(dayjs(filters.value.date_from)));
    }
    if (filters.value.date_to) {
      result = result.filter(q => dayjs(q.created_at).isBefore(dayjs(filters.value.date_to).add(1, 'day')));
    }

    // 金額範圍過濾
    if (filters.value.amount_min) {
      result = result.filter(q => q.final_amount >= parseFloat(filters.value.amount_min));
    }
    if (filters.value.amount_max) {
      result = result.filter(q => q.final_amount <= parseFloat(filters.value.amount_max));
    }

    return result;
  });

  const totalPages = computed(() => {
    return Math.ceil(pagination.value.total / pagination.value.size);
  });

  // 統計數據
  const statistics = computed(() => {
    const all = quotations.value;
    const thisMonth = all.filter(q => dayjs(q.created_at).isSame(dayjs(), 'month'));

    return {
      total: all.length,
      thisMonth: thisMonth.length,
      draft: all.filter(q => q.status === 'draft').length,
      sent: all.filter(q => q.status === 'sent').length,
      accepted: all.filter(q => q.status === 'accepted').length,
      rejected: all.filter(q => q.status === 'rejected').length,
      expired: all.filter(q => q.status === 'expired').length,
      totalAmount: all.reduce((sum, q) => sum + q.final_amount, 0),
      acceptedAmount: all.filter(q => q.status === 'accepted').reduce((sum, q) => sum + q.final_amount, 0),
      thisMonthAmount: thisMonth.reduce((sum, q) => sum + q.final_amount, 0),
      averageAmount: all.length > 0 ? all.reduce((sum, q) => sum + q.final_amount, 0) / all.length : 0,
    };
  });

  // 狀態選項
  const statusOptions = computed(() => [
    { label: '草稿', value: 'draft', color: '#909399' },
    { label: '已發送', value: 'sent', color: '#E6A23C' },
    { label: '已接受', value: 'accepted', color: '#67C23A' },
    { label: '已拒絕', value: 'rejected', color: '#F56C6C' },
    { label: '已過期', value: 'expired', color: '#C0C4CC' },
  ]);

  // 獲取狀態標籤
  const getStatusLabel = (status: QuotationStatus): string => {
    const option = statusOptions.value.find(opt => opt.value === status);
    return option?.label || status;
  };

  // 獲取狀態顏色
  const getStatusColor = (status: QuotationStatus): string => {
    const option = statusOptions.value.find(opt => opt.value === status);
    return option?.color || '#909399';
  };

  // Actions
  const fetchQuotations = async (params: any = {}): Promise<void> => {
    try {
      loading.value = true;
      error.value = null;

      const requestParams = {
        page: pagination.value.page,
        size: pagination.value.size,
        ...filters.value,
        ...params,
      };

      const response = await quotationAPI.getList(requestParams);
      quotations.value = response.data.items;
      pagination.value.total = response.data.total;

    } catch (err: any) {
      error.value = err.message || '獲取報價單列表失敗';
      ElMessage.error(error.value);
      quotations.value = [];
      pagination.value.total = 0;
    } finally {
      loading.value = false;
    }
  };

  const getQuotation = async (id: string | number): Promise<Quotation | null> => {
    try {
      loading.value = true;
      error.value = null;

      const response = await quotationAPI.getById(id);
      currentQuotation.value = response.data;
      return response.data;

    } catch (err: any) {
      error.value = err.message || '獲取報價單詳情失敗';
      ElMessage.error(error.value);
      currentQuotation.value = null;
      return null;
    } finally {
      loading.value = false;
    }
  };

  const createQuotation = async (quotationData: Partial<Quotation>): Promise<Quotation | null> => {
    try {
      submitting.value = true;
      error.value = null;

      const response = await quotationAPI.create(quotationData);
      const newQuotation = response.data;

      // 添加到列表頂部
      quotations.value.unshift(newQuotation);
      pagination.value.total++;

      ElMessage.success('報價單創建成功');
      return newQuotation;

    } catch (err: any) {
      error.value = err.message || '創建報價單失敗';
      ElMessage.error(error.value);
      return null;
    } finally {
      submitting.value = false;
    }
  };

  const updateQuotation = async (id: string | number, quotationData: Partial<Quotation>): Promise<Quotation | null> => {
    try {
      submitting.value = true;
      error.value = null;

      const response = await quotationAPI.update(id, quotationData);
      const updatedQuotation = response.data;

      // 更新列表中的項目
      const index = quotations.value.findIndex(q => q.id === id);
      if (index !== -1) {
        quotations.value[index] = updatedQuotation;
      }

      // 更新當前項目
      if (currentQuotation.value?.id === id) {
        currentQuotation.value = updatedQuotation;
      }

      ElMessage.success('報價單更新成功');
      return updatedQuotation;

    } catch (err: any) {
      error.value = err.message || '更新報價單失敗';
      ElMessage.error(error.value);
      return null;
    } finally {
      submitting.value = false;
    }
  };

  const deleteQuotation = async (id: string | number): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      await quotationAPI.delete(id);

      // 從列表中移除
      const index = quotations.value.findIndex(q => q.id === id);
      if (index !== -1) {
        quotations.value.splice(index, 1);
        pagination.value.total--;
      }

      // 清除當前項目
      if (currentQuotation.value?.id === id) {
        currentQuotation.value = null;
      }

      ElMessage.success('報價單刪除成功');
      return true;

    } catch (err: any) {
      error.value = err.message || '刪除報價單失敗';
      ElMessage.error(error.value);
      return false;
    } finally {
      loading.value = false;
    }
  };

  const updateStatus = async (id: string | number, status: QuotationStatus): Promise<boolean> => {
    try {
      const result = await updateQuotation(id, { status });
      return !!result;
    } catch (err) {
      return false;
    }
  };

  const duplicateQuotation = async (id: string | number): Promise<Quotation | null> => {
    try {
      const original = quotations.value.find(q => q.id === id);
      if (!original) {
        ElMessage.error('找不到要複製的報價單');
        return null;
      }

      const duplicatedData = {
        ...original,
        id: undefined,
        quotation_no: undefined,
        status: 'draft' as QuotationStatus,
        created_at: undefined,
        updated_at: undefined,
      };

      return await createQuotation(duplicatedData);

    } catch (err: any) {
      error.value = err.message || '複製報價單失敗';
      ElMessage.error(error.value);
      return null;
    }
  };

  // 轉換為發票
  const convertToInvoice = async (id: string | number): Promise<boolean> => {
    try {
      submitting.value = true;
      error.value = null;

      const response = await quotationAPI.convertToInvoice(id);

      // 更新報價單狀態
      const index = quotations.value.findIndex(q => q.id === id);
      if (index !== -1) {
        quotations.value[index] = {
          ...quotations.value[index],
          converted_to_invoice: true,
          invoice_id: response.data.invoice_id,
        };
      }

      if (currentQuotation.value?.id === id) {
        currentQuotation.value = {
          ...currentQuotation.value,
          converted_to_invoice: true,
          invoice_id: response.data.invoice_id,
        };
      }

      ElMessage.success('已成功轉換為發票');
      return true;

    } catch (err: any) {
      error.value = err.message || '轉換為發票失敗';
      ElMessage.error(error.value);
      return false;
    } finally {
      submitting.value = false;
    }
  };

  // 轉換為送貨單
  const convertToDeliveryNote = async (id: string | number): Promise<boolean> => {
    try {
      submitting.value = true;
      error.value = null;

      const response = await quotationAPI.convertToDeliveryNote(id);

      // 更新報價單狀態
      const index = quotations.value.findIndex(q => q.id === id);
      if (index !== -1) {
        quotations.value[index] = {
          ...quotations.value[index],
          converted_to_delivery_note: true,
          delivery_note_id: response.data.delivery_note_id,
        };
      }

      if (currentQuotation.value?.id === id) {
        currentQuotation.value = {
          ...currentQuotation.value,
          converted_to_delivery_note: true,
          delivery_note_id: response.data.delivery_note_id,
        };
      }

      ElMessage.success('已成功轉換為送貨單');
      return true;

    } catch (err: any) {
      error.value = err.message || '轉換為送貨單失敗';
      ElMessage.error(error.value);
      return false;
    } finally {
      submitting.value = false;
    }
  };

  // 生成 PDF
  const generatePDF = async (id: string | number): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      await quotationAPI.generatePDF(id);
      ElMessage.success('PDF 生成成功');
      return true;

    } catch (err: any) {
      error.value = err.message || 'PDF 生成失敗';
      ElMessage.error(error.value);
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 批量操作
  const batchOperation = async (operation: string, ids: (string | number)[], params?: any): Promise<boolean> => {
    try {
      submitting.value = true;
      error.value = null;

      // 根據操作類型調用不同的 API
      switch (operation) {
        case 'delete':
          await Promise.all(ids.map(id => quotationAPI.delete(id)));
          // 從列表中移除
          quotations.value = quotations.value.filter(q => !ids.includes(q.id));
          pagination.value.total -= ids.length;
          ElMessage.success(`成功刪除 ${ids.length} 個報價單`);
          break;

        case 'update_status':
          await Promise.all(ids.map(id => quotationAPI.update(id, { status: params.status })));
          // 更新列表中的狀態
          quotations.value.forEach(q => {
            if (ids.includes(q.id)) {
              q.status = params.status;
            }
          });
          ElMessage.success(`成功更新 ${ids.length} 個報價單狀態`);
          break;

        default:
          throw new Error('不支持的批量操作');
      }

      return true;

    } catch (err: any) {
      error.value = err.message || '批量操作失敗';
      ElMessage.error(error.value);
      return false;
    } finally {
      submitting.value = false;
    }
  };

  // 設置篩選條件
  const setFilters = (newFilters: Partial<QuotationFilters>): void => {
    filters.value = { ...filters.value, ...newFilters };
    pagination.value.page = 1; // 重置到第一頁
  };

  // 重置篩選條件
  const resetFilters = (): void => {
    filters.value = {
      search: '',
      status: '',
      customer_id: '',
      date_from: '',
      date_to: '',
      amount_min: '',
      amount_max: '',
    };
    pagination.value.page = 1;
  };

  // 設置分頁
  const setPagination = (page: number, size?: number): void => {
    pagination.value.page = page;
    if (size) {
      pagination.value.size = size;
    }
  };

  // 重置狀態
  const reset = (): void => {
    quotations.value = [];
    currentQuotation.value = null;
    loading.value = false;
    submitting.value = false;
    error.value = null;
    pagination.value = {
      page: 1,
      size: 20,
      total: 0,
    };
    resetFilters();
  };

  return {
    // 狀態
    quotations,
    currentQuotation,
    loading,
    submitting,
    error,
    filters,
    pagination,

    // 計算屬性
    filteredQuotations,
    totalPages,
    statistics,
    statusOptions,
    getStatusLabel,
    getStatusColor,

    // Actions
    fetchQuotations,
    getQuotation,
    createQuotation,
    updateQuotation,
    deleteQuotation,
    updateStatus,
    duplicateQuotation,
    convertToInvoice,
    convertToDeliveryNote,
    generatePDF,
    batchOperation,
    setFilters,
    resetFilters,
    setPagination,
    reset,
  };
}, {
  persist: {
    key: 'hb-engineering-quotation',
    storage: sessionStorage,
    paths: ['filters', 'pagination'],
  },
});