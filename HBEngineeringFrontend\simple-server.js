const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8080;
const DIST_DIR = path.join(__dirname, 'dist');

// MIME 類型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
  let filePath = path.join(DIST_DIR, req.url === '/' ? 'index.html' : req.url);
  
  // 如果文件不存在，返回 index.html (for SPA routing)
  if (!fs.existsSync(filePath)) {
    filePath = path.join(DIST_DIR, 'index.html');
  }
  
  const ext = path.extname(filePath);
  const contentType = mimeTypes[ext] || 'application/octet-stream';
  
  fs.readFile(filePath, (err, content) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('File not found');
      return;
    }
    
    res.writeHead(200, { 
      'Content-Type': contentType,
      'Access-Control-Allow-Origin': '*'
    });
    res.end(content);
  });
  
  console.log(`${new Date().toLocaleTimeString()} ${req.method} ${req.url}`);
});

server.listen(PORT, () => {
  console.log(`🚀 蒼藍工程公司管理系統正在運行！`);
  console.log(`📍 訪問地址: http://localhost:${PORT}`);
  console.log(`🎯 按 Ctrl+C 停止服務器`);
});

// 優雅關閉
process.on('SIGINT', () => {
  console.log('\n⏹️  服務器已停止');
  server.close();
  process.exit(0);
}); 