<template>
  <div class="stat-card" :class="{ 'hoverable': hoverable }">
    <div class="stat-card-header">
      <div class="stat-icon" :style="{ backgroundColor: iconColor }">
        <el-icon :size="24">
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="stat-actions" v-if="$slots.actions">
        <slot name="actions" />
      </div>
    </div>
    
    <div class="stat-card-content">
      <div class="stat-title">{{ title }}</div>
      <div class="stat-value">
        <span v-if="loading" class="loading-placeholder">--</span>
        <span v-else>
          {{ formattedValue }}
        </span>
      </div>
      <div class="stat-description" v-if="description">
        {{ description }}
      </div>
    </div>
    
    <div class="stat-card-footer" v-if="showTrend || $slots.footer">
      <div class="stat-trend" v-if="showTrend">
        <el-icon :class="trendClass">
          <ArrowUp v-if="trend > 0" />
          <ArrowDown v-else-if="trend < 0" />
          <Minus v-else />
        </el-icon>
        <span :class="trendClass">
          {{ Math.abs(trend) }}%
        </span>
        <span class="trend-text">{{ trendText }}</span>
      </div>
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue';
import { formatCurrency, formatNumber } from '@/utils';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: [String, Object],
    required: true
  },
  iconColor: {
    type: String,
    default: '#409EFF'
  },
  type: {
    type: String,
    default: 'number', // number, currency, percentage
    validator: (value) => ['number', 'currency', 'percentage'].includes(value)
  },
  precision: {
    type: Number,
    default: 0
  },
  loading: {
    type: Boolean,
    default: false
  },
  hoverable: {
    type: Boolean,
    default: false
  },
  trend: {
    type: Number,
    default: null
  },
  trendText: {
    type: String,
    default: ''
  },
  showTrend: {
    type: Boolean,
    default: false
  }
});

// 格式化值
const formattedValue = computed(() => {
  if (props.loading) return '--';
  
  const { value, type, precision } = props;
  
  switch (type) {
    case 'currency':
      return formatCurrency(value);
    case 'percentage':
      return `${formatNumber(value, precision)}%`;
    case 'number':
    default:
      return formatNumber(value, precision);
  }
});

// 趨勢樣式
const trendClass = computed(() => {
  if (props.trend === null || props.trend === 0) return 'trend-neutral';
  return props.trend > 0 ? 'trend-up' : 'trend-down';
});
</script>

<style scoped>
.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-card.hoverable:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.stat-actions {
  display: flex;
  gap: 8px;
}

.stat-card-content {
  margin-bottom: 16px;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.loading-placeholder {
  color: #c0c4cc;
}

.stat-description {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.stat-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-neutral {
  color: #909399;
}

.trend-text {
  color: #666;
  margin-left: 4px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
  }
  
  .stat-icon .el-icon {
    font-size: 20px;
  }
}
</style> 