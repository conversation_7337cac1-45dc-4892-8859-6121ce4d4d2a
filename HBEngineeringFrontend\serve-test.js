const express = require('express');
const path = require('path');

const app = express();
const PORT = 8080;

// 設置靜態文件服務
app.use(express.static(path.join(__dirname, 'dist')));

// 處理 SPA 路由 - 所有請求都返回 index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`🚀 蒼藍工程公司管理系統正在運行！`);
  console.log(`📍 訪問地址: http://localhost:${PORT}`);
  console.log(`🎯 按 Ctrl+C 停止服務器`);
});

// 優雅關閉
process.on('SIGINT', () => {
  console.log('\n⏹️  服務器已停止');
  process.exit(0);
}); 