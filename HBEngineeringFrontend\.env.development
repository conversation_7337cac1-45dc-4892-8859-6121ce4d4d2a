# 開發環境配置

# 應用基本配置
VITE_APP_TITLE=蒼藍工程公司管理系統 (開發版)
VITE_APP_VERSION=2.0.0-dev
VITE_APP_DESCRIPTION=現代化企業管理平台 - 開發環境

# API 配置
VITE_API_BASE_URL=http://localhost:5000/api
VITE_API_TIMEOUT=30000

# 後端服務配置
VITE_BACKEND_HOST=localhost
VITE_BACKEND_PORT=5000

# 安全配置
VITE_ENCRYPTION_KEY=dev-encryption-key-not-for-production
VITE_JWT_SECRET=dev-jwt-secret-not-for-production

# 功能開關
VITE_ENABLE_MOCK=true
VITE_ENABLE_DEBUG=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false

# 系統配置
VITE_SESSION_TIMEOUT=3600000
VITE_AUTO_LOGOUT_WARNING=300000
VITE_MAINTENANCE_MODE=false

# 開發配置
VITE_DEV_SERVER_PORT=3000
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_OPEN_BROWSER=true

# 構建配置
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_MINIFY=false
VITE_BUILD_ANALYZE=false

# 日誌配置
VITE_LOG_LEVEL=debug
VITE_LOG_TO_CONSOLE=true
VITE_LOG_TO_FILE=true

# 緩存配置
VITE_CACHE_ENABLED=false
VITE_CACHE_TTL=60000

# 性能配置
VITE_ENABLE_PWA=false
VITE_ENABLE_COMPRESSION=false
VITE_VIRTUAL_SCROLL_THRESHOLD=50

# 安全配置
VITE_ENABLE_CSP=false
VITE_RATE_LIMIT_REQUESTS=1000
VITE_RATE_LIMIT_WINDOW=60000
