// 測試 API 連接
const axios = require('axios');

async function testAPI() {
  try {
    console.log('🔍 測試後端 API 連接...\n');
    
    // 1. 測試健康檢查
    console.log('1. 測試健康檢查 API:');
    const healthResponse = await axios.get('http://localhost:5000/api/system/health');
    console.log('✅ 健康檢查成功:', healthResponse.data);
    console.log('');
    
    // 2. 測試登入 API
    console.log('2. 測試登入 API:');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    console.log('✅ 登入成功:', loginResponse.data);
    console.log('');
    
    // 3. 測試報價單列表 API
    console.log('3. 測試報價單列表 API:');
    const quotationsResponse = await axios.get('http://localhost:5000/api/quotations', {
      headers: {
        'Authorization': `Bearer ${loginResponse.data.access_token}`
      }
    });
    console.log('✅ 報價單列表獲取成功:', quotationsResponse.data);
    console.log('');
    
    // 4. 測試客戶列表 API
    console.log('4. 測試客戶列表 API:');
    const customersResponse = await axios.get('http://localhost:5000/api/customers', {
      headers: {
        'Authorization': `Bearer ${loginResponse.data.access_token}`
      }
    });
    console.log('✅ 客戶列表獲取成功:', customersResponse.data);
    console.log('');
    
    console.log('🎉 所有 API 測試通過！');
    
  } catch (error) {
    console.error('❌ API 測試失敗:', error.response?.data || error.message);
  }
}

testAPI();
