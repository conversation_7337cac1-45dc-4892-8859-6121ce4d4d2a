import { test, expect } from '@playwright/test';

test.describe('登入功能', () => {
  test.beforeEach(async ({ page }) => {
    // 導航到登入頁面
    await page.goto('/login');
  });

  test('應該顯示登入表單', async ({ page }) => {
    // 檢查頁面標題
    await expect(page).toHaveTitle(/登入.*蒼藍工程公司管理系統/);
    
    // 檢查表單元素
    await expect(page.locator('[data-testid="username-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
    
    // 檢查登入按鈕初始狀態
    await expect(page.locator('[data-testid="login-button"]')).toBeEnabled();
  });

  test('應該驗證必填字段', async ({ page }) => {
    // 點擊登入按鈕而不填寫任何信息
    await page.click('[data-testid="login-button"]');
    
    // 檢查錯誤消息
    await expect(page.locator('.el-form-item__error')).toContainText('請輸入用戶名');
  });

  test('應該顯示密碼錯誤消息', async ({ page }) => {
    // 填寫用戶名但不填寫密碼
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.click('[data-testid="login-button"]');
    
    // 檢查密碼錯誤消息
    await expect(page.locator('.el-form-item__error')).toContainText('請輸入密碼');
  });

  test('應該處理登入失敗', async ({ page }) => {
    // Mock API 響應
    await page.route('/api/auth/login', async route => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: '用戶名或密碼錯誤'
        })
      });
    });

    // 填寫錯誤的登入信息
    await page.fill('[data-testid="username-input"]', 'wronguser');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');

    // 檢查錯誤消息
    await expect(page.locator('.el-message--error')).toContainText('用戶名或密碼錯誤');
  });

  test('應該成功登入並重定向到儀表板', async ({ page }) => {
    // Mock 成功的登入響應
    await page.route('/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            access_token: 'mock-token',
            refresh_token: 'mock-refresh-token',
            user: {
              id: 1,
              username: 'testuser',
              name: '測試用戶',
              email: '<EMAIL>',
              role: 'admin'
            },
            permissions: []
          }
        })
      });
    });

    // Mock 用戶信息請求
    await page.route('/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: {
              id: 1,
              username: 'testuser',
              name: '測試用戶',
              email: '<EMAIL>',
              role: 'admin'
            },
            permissions: []
          }
        })
      });
    });

    // 填寫正確的登入信息
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');

    // 等待重定向到儀表板
    await expect(page).toHaveURL('/dashboard');
    
    // 檢查成功消息
    await expect(page.locator('.el-message--success')).toContainText('登入成功');
  });

  test('應該記住登入狀態', async ({ page }) => {
    // Mock 登入響應
    await page.route('/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            access_token: 'mock-token',
            refresh_token: 'mock-refresh-token',
            user: {
              id: 1,
              username: 'testuser',
              name: '測試用戶',
              email: '<EMAIL>',
              role: 'admin'
            },
            permissions: []
          }
        })
      });
    });

    // 勾選記住我
    await page.check('[data-testid="remember-checkbox"]');
    
    // 登入
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');

    // 等待重定向
    await expect(page).toHaveURL('/dashboard');

    // 檢查 localStorage 中是否保存了 token
    const token = await page.evaluate(() => localStorage.getItem('token'));
    expect(token).toBeTruthy();
  });

  test('應該支持鍵盤操作', async ({ page }) => {
    // 使用 Tab 鍵導航
    await page.keyboard.press('Tab'); // 聚焦到用戶名輸入框
    await page.keyboard.type('testuser');
    
    await page.keyboard.press('Tab'); // 聚焦到密碼輸入框
    await page.keyboard.type('password123');
    
    await page.keyboard.press('Tab'); // 聚焦到記住我複選框
    await page.keyboard.press('Space'); // 勾選記住我
    
    await page.keyboard.press('Tab'); // 聚焦到登入按鈕
    
    // 檢查聚焦狀態
    await expect(page.locator('[data-testid="login-button"]')).toBeFocused();
  });

  test('應該在密碼輸入框按 Enter 鍵提交表單', async ({ page }) => {
    // Mock 登入響應
    await page.route('/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            access_token: 'mock-token',
            refresh_token: 'mock-refresh-token',
            user: {
              id: 1,
              username: 'testuser',
              name: '測試用戶',
              email: '<EMAIL>',
              role: 'admin'
            },
            permissions: []
          }
        })
      });
    });

    // 填寫表單
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'password123');
    
    // 在密碼輸入框按 Enter 鍵
    await page.locator('[data-testid="password-input"]').press('Enter');

    // 等待重定向
    await expect(page).toHaveURL('/dashboard');
  });

  test('應該顯示加載狀態', async ({ page }) => {
    // Mock 慢速響應
    await page.route('/api/auth/login', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 延遲 1 秒
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            access_token: 'mock-token',
            refresh_token: 'mock-refresh-token',
            user: {
              id: 1,
              username: 'testuser',
              name: '測試用戶',
              email: '<EMAIL>',
              role: 'admin'
            },
            permissions: []
          }
        })
      });
    });

    // 填寫表單並提交
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');

    // 檢查加載狀態
    await expect(page.locator('[data-testid="login-button"]')).toBeDisabled();
    await expect(page.locator('[data-testid="login-button"] .el-icon')).toBeVisible();
    
    // 等待加載完成
    await expect(page).toHaveURL('/dashboard');
  });

  test('應該處理網絡錯誤', async ({ page }) => {
    // Mock 網絡錯誤
    await page.route('/api/auth/login', async route => {
      await route.abort('failed');
    });

    // 填寫表單並提交
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');

    // 檢查錯誤消息
    await expect(page.locator('.el-message--error')).toContainText('網絡連接失敗');
  });

  test('應該支持多語言', async ({ page }) => {
    // 切換到英文
    await page.click('[data-testid="language-selector"]');
    await page.click('[data-testid="language-en"]');

    // 檢查英文標籤
    await expect(page.locator('[data-testid="username-label"]')).toContainText('Username');
    await expect(page.locator('[data-testid="password-label"]')).toContainText('Password');
    await expect(page.locator('[data-testid="login-button"]')).toContainText('Login');

    // 切換回中文
    await page.click('[data-testid="language-selector"]');
    await page.click('[data-testid="language-zh"]');

    // 檢查中文標籤
    await expect(page.locator('[data-testid="username-label"]')).toContainText('用戶名');
    await expect(page.locator('[data-testid="password-label"]')).toContainText('密碼');
    await expect(page.locator('[data-testid="login-button"]')).toContainText('登入');
  });
});
