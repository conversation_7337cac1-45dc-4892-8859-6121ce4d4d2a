import apiClient from './client';

// 認證相關API
export const authAPI = {
  login: (credentials) => apiClient.post('/auth/login', credentials),
  register: (userInfo) => apiClient.post('/auth/register', userInfo),
  logout: () => apiClient.post('/auth/logout')
};

// 客戶管理API
export const customerAPI = {
  getList: (params) => apiClient.get('/customers', { params }),
  create: (data) => apiClient.post('/customers', data),
  update: (id, data) => apiClient.put(`/customers/${id}`, data),
  delete: (id) => apiClient.delete(`/customers/${id}`),
  getById: (id) => apiClient.get(`/customers/${id}`)
};

// 產品管理API
export const productAPI = {
  getList: (params) => apiClient.get('/products', { params }),
  create: (data) => apiClient.post('/products', data),
  update: (id, data) => apiClient.put(`/products/${id}`, data),
  delete: (id) => apiClient.delete(`/products/${id}`),
  getById: (id) => apiClient.get(`/products/${id}`)
};

// 報價單管理API
export const quotationAPI = {
  getList: (params) => apiClient.get('/quotations', { params }),
  create: (data) => apiClient.post('/quotations', data),
  update: (id, data) => apiClient.put(`/quotations/${id}`, data),
  delete: (id) => apiClient.delete(`/quotations/${id}`),
  getById: (id) => apiClient.get(`/quotations/${id}`),
  generatePDF: (id) => apiClient.get(`/quotations/${id}/pdf`, { responseType: 'blob' }),
  convertToInvoice: (id) => apiClient.post(`/quotations/${id}/convert-to-invoice`),
  convertToDeliveryNote: (id) => apiClient.post(`/quotations/${id}/convert-to-delivery-note`)
};

// 發票管理API
export const invoiceAPI = {
  getList: (params) => apiClient.get('/invoices', { params }),
  create: (data) => apiClient.post('/invoices', data),
  update: (id, data) => apiClient.put(`/invoices/${id}`, data),
  delete: (id) => apiClient.delete(`/invoices/${id}`),
  getById: (id) => apiClient.get(`/invoices/${id}`),
  generatePDF: (id) => apiClient.get(`/invoices/${id}/pdf`, { responseType: 'blob' }),
  markAsPaid: (id) => apiClient.post(`/invoices/${id}/mark-paid`)
};

// 送貨單管理API
export const deliveryNoteAPI = {
  getList: (params) => apiClient.get('/delivery-notes', { params }),
  create: (data) => apiClient.post('/delivery-notes', data),
  update: (id, data) => apiClient.put(`/delivery-notes/${id}`, data),
  delete: (id) => apiClient.delete(`/delivery-notes/${id}`),
  getById: (id) => apiClient.get(`/delivery-notes/${id}`),
  generatePDF: (id) => apiClient.get(`/delivery-notes/${id}/pdf`, { responseType: 'blob' }),
  addSignature: (id, signature) => apiClient.post(`/delivery-notes/${id}/signature`, signature)
};

// 系統API
export const systemAPI = {
  getHealth: () => apiClient.get('/system/health'),
  getSettings: () => apiClient.get('/system/settings'),
  updateSettings: (data) => apiClient.put('/system/settings', data)
};

export default {
  auth: authAPI,
  customer: customerAPI,
  product: productAPI,
  quotation: quotationAPI,
  invoice: invoiceAPI,
  deliveryNote: deliveryNoteAPI,
  system: systemAPI
}; 