import "./chunk-5WRI5ZAA.js";

// node_modules/element-plus/dist/locale/zh-hk.mjs
var zhHk = {
  name: "zh-hk",
  el: {
    breadcrumb: {
      label: "麵包屑"
    },
    colorpicker: {
      confirm: "確認",
      clear: "清空",
      defaultLabel: "顏色選擇器",
      description: "當前顏色為 {color}。按 Enter 鍵選擇新顏色。",
      alphaLabel: "選擇透明度的值"
    },
    datepicker: {
      now: "現在",
      today: "今天",
      cancel: "取消",
      clear: "清空",
      confirm: "確認",
      dateTablePrompt: "使用方向鍵與 Enter 鍵以選擇日期",
      monthTablePrompt: "使用方向鍵與 Enter 鍵以選擇月份",
      yearTablePrompt: "使用方向鍵與 Enter 鍵以選擇年份",
      selectedDate: "已選日期",
      selectDate: "選擇日期",
      selectTime: "選擇時間",
      startDate: "開始日期",
      startTime: "開始時間",
      endDate: "結束日期",
      endTime: "結束時間",
      prevYear: "前一年",
      nextYear: "後一年",
      prevMonth: "上個月",
      nextMonth: "下個月",
      year: "年",
      month1: "1 月",
      month2: "2 月",
      month3: "3 月",
      month4: "4 月",
      month5: "5 月",
      month6: "6 月",
      month7: "7 月",
      month8: "8 月",
      month9: "9 月",
      month10: "10 月",
      month11: "11 月",
      month12: "12 月",
      weeks: {
        sun: "日",
        mon: "一",
        tue: "二",
        wed: "三",
        thu: "四",
        fri: "五",
        sat: "六"
      },
      weeksFull: {
        sun: "星期日",
        mon: "星期一",
        tue: "星期二",
        wed: "星期三",
        thu: "星期四",
        fri: "星期五",
        sat: "星期六"
      },
      months: {
        jan: "一月",
        feb: "二月",
        mar: "三月",
        apr: "四月",
        may: "五月",
        jun: "六月",
        jul: "七月",
        aug: "八月",
        sep: "九月",
        oct: "十月",
        nov: "十一月",
        dec: "十二月"
      }
    },
    inputNumber: {
      decrease: "減少數值",
      increase: "增加數值"
    },
    select: {
      loading: "載入中",
      noMatch: "無匹配資料",
      noData: "無資料",
      placeholder: "請選擇"
    },
    mention: {
      loading: "載入中"
    },
    dropdown: {
      toggleDropdown: "切換下拉選單"
    },
    cascader: {
      noMatch: "無匹配資料",
      loading: "載入中",
      placeholder: "請選擇",
      noData: "無資料"
    },
    pagination: {
      goto: "前往",
      pagesize: "項/頁",
      total: "共 {total} 項",
      pageClassifier: "頁",
      page: "頁",
      prev: "上一頁",
      next: "下一頁",
      currentPage: "第 {pager} 頁",
      prevPages: "向前 {pager} 頁",
      nextPages: "向後 {pager} 頁",
      deprecationWarning: "檢測到已過時的使用方式，請參閱 el-pagination 說明文件以了解更多資訊"
    },
    dialog: {
      close: "關閉此對話框"
    },
    drawer: {
      close: "關閉此對話框"
    },
    messagebox: {
      title: "提示",
      confirm: "確定",
      cancel: "取消",
      error: "輸入的資料不符合規定!",
      close: "關閉此對話框"
    },
    upload: {
      deleteTip: "按 Delete 鍵以刪除",
      delete: "刪除",
      preview: "查看圖片",
      continue: "繼續上傳"
    },
    slider: {
      defaultLabel: "滑桿介於 {min} 至 {max}",
      defaultRangeStartLabel: "選擇起始值",
      defaultRangeEndLabel: "選擇結束值"
    },
    table: {
      emptyText: "暫無資料",
      confirmFilter: "篩選",
      resetFilter: "重置",
      clearFilter: "全部",
      sumText: "合計"
    },
    tour: {
      next: "下一步",
      previous: "上一步",
      finish: "結束導覽"
    },
    tree: {
      emptyText: "暫無資料"
    },
    transfer: {
      noMatch: "無匹配資料",
      noData: "無資料",
      titles: ["列表 1", "列表 2"],
      filterPlaceholder: "請輸入搜尋內容",
      noCheckedFormat: "共 {total} 項",
      hasCheckedFormat: "已選 {checked}/{total} 項"
    },
    image: {
      error: "載入失敗"
    },
    pageHeader: {
      title: "返回"
    },
    popconfirm: {
      confirmButtonText: "確認",
      cancelButtonText: "取消"
    },
    carousel: {
      leftArrow: "上一張幻燈片",
      rightArrow: "下一張幻燈片",
      indicator: "幻燈片切換至索引 {index}"
    }
  }
};
export {
  zhHk as default
};
/*! Bundled license information:

element-plus/dist/locale/zh-hk.mjs:
  (*! Element Plus v2.10.1 *)
*/
//# sourceMappingURL=element-plus_dist_locale_zh-hk__mjs.js.map
