# 🏗️ 蒼藍工程公司管理系統 v2.0

[![Vue 3](https://img.shields.io/badge/Vue-3.4+-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-3178C6?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Electron](https://img.shields.io/badge/Electron-30.0+-47848F?style=flat-square&logo=electron)](https://www.electronjs.org/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.7+-409EFF?style=flat-square&logo=element)](https://element-plus.org/)
[![Vite](https://img.shields.io/badge/Vite-5.0+-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

> 🚀 現代化企業管理平台 - 專為工程公司設計的全功能桌面應用程序

## ✨ 功能特色

### 📊 核心業務模塊
- 📋 **報價單管理** - 智能報價、模板管理、狀態跟踪
- 📄 **發票管理** - 自動轉換、批量處理、財務對接
- 🚚 **送貨單管理** - 物流跟踪、狀態更新、客戶確認
- 👥 **客戶管理** - CRM 功能、客戶分析、歷史記錄
- 📦 **產品管理** - 庫存控制、分類管理、價格策略

### 🛡️ 安全與性能
- 🔐 **多層安全防護** - JWT 認證、權限控制、數據加密
- ⚡ **高性能優化** - 虛擬滾動、懶加載、Web Workers
- 🎯 **智能緩存** - 多級緩存、離線支持、數據同步
- 📱 **響應式設計** - 適配各種屏幕尺寸、觸控支持

### 🌍 用戶體驗
- 🌐 **多語言支持** - 中文繁體/簡體、英文
- 🎨 **現代化 UI** - Material Design、深色模式、自定義主題
- ♿ **無障礙設計** - WCAG 2.1 AA 標準、鍵盤導航
- 📊 **數據可視化** - 圖表分析、報表生成、趨勢預測

## 🏗️ 技術架構

### 前端技術棧
```
Vue 3.4 + TypeScript 5.0
├── 🎨 UI 框架: Element Plus 2.7
├── ⚡ 構建工具: Vite 5.0
├── 🗃️ 狀態管理: Pinia 2.1 + 持久化
├── 🛣️ 路由管理: Vue Router 4.3
├── 🌐 國際化: Vue I18n 9.13
├── 🎭 樣式方案: SCSS + CSS Variables
├── 🧪 測試框架: Vitest + Playwright
└── 📱 桌面應用: Electron 30.0
```

### 開發工具鏈
```
現代化開發體驗
├── 📦 包管理: npm/yarn/pnpm
├── 🔍 代碼檢查: ESLint + Prettier
├── 🏷️ 類型檢查: TypeScript + Vue TSC
├── 🔧 自動導入: unplugin-auto-import
├── 📊 構建分析: rollup-plugin-visualizer
├── 🚀 熱重載: Vite HMR
└── 📋 Git 鉤子: Husky + lint-staged
```

## 🚀 快速開始

### 環境要求
- **Node.js** >= 18.0.0
- **npm** >= 9.0.0 或 **yarn** >= 1.22.0
- **Git** >= 2.30.0

### 安裝步驟

1. **克隆項目**
```bash
git clone https://github.com/your-org/hb-engineering-system.git
cd hb-engineering-system/HBEngineeringFrontend
```

2. **安裝依賴**
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

3. **環境配置**
```bash
# 複製環境變量模板
cp .env.example .env.development

# 編輯環境變量
nano .env.development
```

4. **啟動開發服務器**
```bash
npm run dev
```

5. **構建生產版本**
```bash
# Web 版本
npm run build

# 桌面應用
npm run build:win    # Windows
npm run build:mac    # macOS  
npm run build:linux  # Linux
```

### 開發命令

```bash
# 開發相關
npm run dev          # 啟動開發服務器
npm run dev:vite     # 僅啟動 Vite 服務器
npm run dev:electron # 僅啟動 Electron

# 構建相關
npm run build        # 構建 Web 版本
npm run preview      # 預覽構建結果
npm run build:win    # 構建 Windows 應用
npm run build:mac    # 構建 macOS 應用
npm run build:linux  # 構建 Linux 應用

# 測試相關
npm run test         # 運行單元測試
npm run test:ui      # 測試 UI 界面
npm run test:coverage # 測試覆蓋率報告
npm run test:e2e     # 端到端測試

# 代碼質量
npm run lint         # 代碼檢查
npm run lint:fix     # 自動修復
npm run format       # 代碼格式化
npm run type-check   # 類型檢查
```

## 📁 項目結構

```
HBEngineeringFrontend/
├── 📁 public/                    # 靜態資源
│   ├── 🖼️ icons/                # 應用圖標
│   ├── 🌐 locales/              # 語言文件
│   └── 👷 workers/              # Web Workers
├── 📁 src/
│   ├── 📁 api/                  # API 接口層
│   │   ├── 📄 client.ts         # HTTP 客戶端
│   │   └── 📄 index.ts          # API 統一導出
│   ├── 📁 renderer/             # 渲染進程
│   │   ├── 📁 components/       # 可復用組件
│   │   │   ├── 📁 common/       # 通用組件
│   │   │   ├── 📁 business/     # 業務組件
│   │   │   └── 📁 layout/       # 布局組件
│   │   ├── 📁 views/            # 頁面視圖
│   │   │   ├── 📁 dashboard/    # 儀表板
│   │   │   ├── 📁 quotation/    # 報價單
│   │   │   ├── 📁 invoice/      # 發票
│   │   │   ├── 📁 delivery/     # 送貨單
│   │   │   ├── 📁 customer/     # 客戶管理
│   │   │   └── 📁 product/      # 產品管理
│   │   ├── 📁 stores/           # 狀態管理
│   │   │   ├── 📄 user.ts       # 用戶狀態
│   │   │   ├── 📄 quotation.ts  # 報價單狀態
│   │   │   └── 📄 index.ts      # Store 統一導出
│   │   ├── 📁 router/           # 路由配置
│   │   │   ├── 📄 index.ts      # 路由定義
│   │   │   └── 📄 guards.ts     # 路由守衛
│   │   ├── 📁 composables/      # 組合式函數
│   │   │   ├── 📄 useTable.ts   # 表格邏輯
│   │   │   ├── 📄 useForm.ts    # 表單邏輯
│   │   │   └── 📄 useSecurity.ts # 安全邏輯
│   │   ├── 📁 utils/            # 工具函數
│   │   │   ├── 📄 security.ts   # 安全工具
│   │   │   ├── 📄 format.ts     # 格式化工具
│   │   │   └── 📄 validation.ts # 驗證工具
│   │   ├── 📁 styles/           # 樣式文件
│   │   │   ├── 📄 variables.scss # SCSS 變量
│   │   │   ├── 📄 mixins.scss   # SCSS 混入
│   │   │   └── 📄 main.scss     # 主樣式
│   │   └── 📁 assets/           # 資源文件
│   ├── 📁 types/                # TypeScript 類型定義
│   │   ├── 📄 user.ts           # 用戶類型
│   │   ├── 📄 quotation.ts      # 報價單類型
│   │   └── 📄 global.d.ts       # 全局類型
│   ├── 📁 config/               # 配置文件
│   │   └── 📄 index.ts          # 應用配置
│   └── 📁 test/                 # 測試文件
│       ├── 📁 unit/             # 單元測試
│       ├── 📁 e2e/              # 端到端測試
│       └── 📄 setup.ts          # 測試設置
├── 📄 package.json              # 項目配置
├── 📄 vite.config.ts            # Vite 配置
├── 📄 tsconfig.json             # TypeScript 配置
├── 📄 vitest.config.ts          # 測試配置
├── 📄 playwright.config.ts      # E2E 測試配置
├── 📄 .eslintrc.cjs             # ESLint 配置
├── 📄 .prettierrc               # Prettier 配置
└── 📄 README.md                 # 項目文檔
```

## 🎯 核心功能詳解

### 📋 報價單管理系統
- **智能編號**: 自動生成格式化編號 (QT-YYYYMM-XXXX)
- **模板系統**: 可復用的報價單模板
- **狀態流轉**: 草稿 → 已發送 → 已接受/已拒絕 → 已過期
- **轉換功能**: 一鍵轉換為發票或送貨單
- **批量操作**: 支持批量導出、狀態更新、刪除
- **歷史追蹤**: 完整的操作歷史記錄

### 📄 發票管理系統
- **自動轉換**: 從報價單無縫轉換
- **付款追蹤**: 未付款、部分付款、已付款狀態
- **稅務計算**: 自動計算稅額和總金額
- **對賬功能**: 與客戶對賬記錄
- **財務報表**: 收入統計和趨勢分析

### 🚚 送貨單管理系統
- **物流跟踪**: 實時更新送貨狀態
- **電子簽收**: 支持電子簽名確認
- **GPS 定位**: 送貨地點定位記錄
- **照片上傳**: 送貨現場照片記錄
- **異常處理**: 送貨異常情況記錄和處理

### 👥 客戶關係管理
- **360度視圖**: 客戶完整信息檔案
- **交易歷史**: 所有業務往來記錄
- **信用管理**: 客戶信用額度和評級
- **溝通記錄**: 電話、郵件、會議記錄
- **客戶分析**: 消費習慣和偏好分析

### 📦 產品庫存管理
- **多維分類**: 按類別、品牌、規格分類
- **庫存預警**: 低庫存自動提醒
- **價格策略**: 階梯定價、批量折扣
- **供應商管理**: 供應商信息和採購記錄
- **成本分析**: 成本構成和利潤分析

## 🔧 開發指南

### 代碼規範
- **命名約定**: 組件使用 PascalCase，文件使用 kebab-case
- **TypeScript**: 嚴格類型檢查，避免使用 any
- **Vue 3**: 優先使用 Composition API 和 `<script setup>`
- **CSS**: 使用 SCSS 變量和混入，避免內聯樣式
- **提交信息**: 遵循 Conventional Commits 規範

### 測試策略
- **單元測試**: 覆蓋率目標 80%+
- **集成測試**: 關鍵業務流程測試
- **E2E 測試**: 用戶關鍵路徑測試
- **性能測試**: 頁面加載和響應時間測試
- **安全測試**: XSS、CSRF 等安全漏洞測試

### 部署流程
1. **開發環境**: 本地開發和測試
2. **測試環境**: 功能測試和用戶驗收
3. **預生產環境**: 性能測試和安全掃描
4. **生產環境**: 正式發布和監控

## 📊 性能優化

### 前端優化
- **代碼分割**: 路由級別的懶加載
- **樹搖優化**: 移除未使用的代碼
- **資源壓縮**: Gzip/Brotli 壓縮
- **緩存策略**: 瀏覽器緩存和 CDN 緩存
- **圖片優化**: WebP 格式和響應式圖片

### 運行時優化
- **虛擬滾動**: 大列表性能優化
- **防抖節流**: 用戶輸入優化
- **內存管理**: 避免內存洩漏
- **Web Workers**: 計算密集型任務
- **Service Worker**: 離線支持和緩存

## 🛡️ 安全措施

### 前端安全
- **XSS 防護**: 輸入驗證和輸出編碼
- **CSRF 防護**: Token 驗證機制
- **內容安全策略**: CSP 頭部配置
- **安全傳輸**: HTTPS 強制使用
- **敏感數據**: 本地加密存儲

### 認證授權
- **JWT Token**: 無狀態認證機制
- **權限控制**: 基於角色的訪問控制
- **會話管理**: 自動過期和刷新
- **多因素認證**: 支持 2FA 驗證
- **審計日誌**: 操作記錄和追蹤

## 📈 監控與維護

### 性能監控
- **頁面性能**: 加載時間和渲染性能
- **API 響應**: 接口響應時間監控
- **錯誤追蹤**: 前端錯誤收集和分析
- **用戶行為**: 用戶操作路徑分析
- **資源使用**: CPU 和內存使用情況

### 日誌管理
- **結構化日誌**: JSON 格式日誌輸出
- **日誌級別**: Debug、Info、Warn、Error
- **日誌輪轉**: 自動歸檔和清理
- **實時監控**: 關鍵錯誤實時告警
- **日誌分析**: ELK 堆棧日誌分析

## 🤝 貢獻指南

### 開發流程
1. **Fork 項目**: 創建個人分支
2. **創建分支**: 功能分支開發
3. **編寫代碼**: 遵循代碼規範
4. **編寫測試**: 確保測試覆蓋率
5. **提交 PR**: 詳細描述變更內容

### 代碼審查
- **功能完整性**: 確保功能正常工作
- **代碼質量**: 遵循最佳實踐
- **性能影響**: 評估性能影響
- **安全考慮**: 檢查安全漏洞
- **文檔更新**: 同步更新文檔

## 📄 許可證

本項目採用 [MIT 許可證](LICENSE)。

## 📞 聯繫我們

- **項目維護者**: 蒼藍工程公司開發團隊
- **技術支持**: <EMAIL>
- **問題反饋**: [GitHub Issues](https://github.com/your-org/hb-engineering-system/issues)
- **功能建議**: [GitHub Discussions](https://github.com/your-org/hb-engineering-system/discussions)

---

<div align="center">
  <p>🏗️ 用心打造，專業可靠</p>
  <p>© 2024 蒼藍工程公司. 保留所有權利.</p>
</div>
