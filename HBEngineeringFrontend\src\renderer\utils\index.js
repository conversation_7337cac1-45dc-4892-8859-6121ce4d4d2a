/**
 * 工具函數集合
 */

// 日期格式化
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

// 貨幣格式化
export const formatCurrency = (amount, currency = 'HKD') => {
  if (!amount && amount !== 0) return '';
  const formatter = new Intl.NumberFormat('zh-HK', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  });
  return formatter.format(amount);
};

// 數字格式化
export const formatNumber = (number, decimals = 2) => {
  if (!number && number !== 0) return '';
  return new Intl.NumberFormat('zh-HK', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number);
};

// 文件大小格式化
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 生成唯一ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 深度克隆
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

// 防抖函數
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 節流函數
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 表單驗證規則
export const validationRules = {
  required: (message = '此欄位為必填') => ({ required: true, message, trigger: 'blur' }),
  email: (message = '請輸入有效的電郵地址') => ({
    type: 'email',
    message,
    trigger: 'blur'
  }),
  phone: (message = '請輸入有效的電話號碼') => ({
    pattern: /^[0-9\-\+\s\(\)]+$/,
    message,
    trigger: 'blur'
  }),
  minLength: (min, message) => ({
    min,
    message: message || `最少需要 ${min} 個字符`,
    trigger: 'blur'
  }),
  maxLength: (max, message) => ({
    max,
    message: message || `最多允許 ${max} 個字符`,
    trigger: 'blur'
  }),
  positiveNumber: (message = '請輸入正數') => ({
    validator: (rule, value, callback) => {
      if (!value || parseFloat(value) > 0) {
        callback();
      } else {
        callback(new Error(message));
      }
    },
    trigger: 'blur'
  })
};

// 狀態標籤顏色映射
export const getStatusColor = (status) => {
  const colorMap = {
    'draft': '#909399',
    'pending': '#E6A23C',
    'approved': '#67C23A',
    'rejected': '#F56C6C',
    'completed': '#409EFF',
    'cancelled': '#F56C6C',
    'active': '#67C23A',
    'inactive': '#909399'
  };
  return colorMap[status] || '#909399';
};

// 下載文件
export const downloadFile = (url, filename) => {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 匯出為 CSV
export const exportToCSV = (data, filename = 'export.csv') => {
  if (!data.length) return;
  
  const headers = Object.keys(data[0]);
  const csv = [
    headers.join(','),
    ...data.map(row => headers.map(field => {
      const value = row[field];
      return typeof value === 'string' && value.includes(',') 
        ? `"${value}"` 
        : value;
    }).join(','))
  ].join('\n');
  
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
};

// 錯誤處理
export const handleError = (error, showMessage = true) => {
  console.error('Error:', error);
  
  if (showMessage && window.ElMessage) {
    const message = error.response?.data?.message || error.message || '發生未知錯誤';
    window.ElMessage.error(message);
  }
  
  return error;
}; 