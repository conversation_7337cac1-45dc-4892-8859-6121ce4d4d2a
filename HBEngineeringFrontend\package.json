{"name": "hb-engineering-frontend", "version": "1.0.0", "description": "蒼藍工程公司管理系統前端", "main": "main.js", "scripts": {"start": "electron .", "dev": "concurrently \"vite\" \"wait-on http://localhost:3000 && electron .\"", "dev:vite": "vite", "dev:electron": "electron .", "test": "electron test.js", "build": "vite build", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "author": "蒼藍工程公司", "license": "ISC", "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.0", "cors": "^2.8.5", "dayjs": "^1.11.0", "element-plus": "^2.4.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "pinia": "^2.1.0", "vue": "^3.3.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.5.0", "concurrently": "^8.2.0", "electron": "^28.0.0", "electron-builder": "^24.0.0", "express": "^5.1.0", "sass": "^1.69.0", "typescript": "^5.0.0", "vite": "^5.0.0", "wait-on": "^7.2.0"}, "keywords": []}