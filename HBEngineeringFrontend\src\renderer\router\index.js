import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    component: () => import('../layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '儀表板' }
      },
      {
        path: 'quotations',
        name: 'Quotations',
        component: () => import('../views/quotations/QuotationList.vue'),
        meta: { title: '報價單管理' }
      },
      {
        path: 'quotations/create',
        name: 'CreateQuotation',
        component: () => import('../views/quotations/QuotationForm.vue'),
        meta: { title: '創建報價單' }
      },
      {
        path: 'quotations/edit/:id',
        name: 'EditQuotation',
        component: () => import('../views/quotations/QuotationForm.vue'),
        meta: { title: '編輯報價單' }
      },
      {
        path: 'invoices',
        name: 'Invoices',
        component: () => import('../views/invoices/InvoiceList.vue'),
        meta: { title: '發票管理' }
      },
      {
        path: 'invoices/create',
        name: 'CreateInvoice',
        component: () => import('../views/invoices/InvoiceForm.vue'),
        meta: { title: '創建發票' }
      },
      {
        path: 'invoices/edit/:id',
        name: 'EditInvoice',
        component: () => import('../views/invoices/InvoiceForm.vue'),
        meta: { title: '編輯發票' }
      },
      {
        path: 'delivery-notes',
        name: 'DeliveryNotes',
        component: () => import('../views/delivery-notes/DeliveryNoteList.vue'),
        meta: { title: '送貨單管理' }
      },
      {
        path: 'delivery-notes/create',
        name: 'CreateDeliveryNote',
        component: () => import('../views/delivery-notes/DeliveryNoteForm.vue'),
        meta: { title: '創建送貨單' }
      },
      {
        path: 'delivery-notes/edit/:id',
        name: 'EditDeliveryNote',
        component: () => import('../views/delivery-notes/DeliveryNoteForm.vue'),
        meta: { title: '編輯送貨單' }
      },
      {
        path: 'customers',
        name: 'Customers',
        component: () => import('../views/customers/CustomerList.vue'),
        meta: { title: '客戶管理' }
      },
      {
        path: 'customers/create',
        name: 'CreateCustomer',
        component: () => import('../views/customers/CustomerForm.vue'),
        meta: { title: '創建客戶' }
      },
      {
        path: 'customers/edit/:id',
        name: 'EditCustomer',
        component: () => import('../views/customers/CustomerForm.vue'),
        meta: { title: '編輯客戶' }
      },
      {
        path: 'products',
        name: 'Products',
        component: () => import('../views/products/ProductList.vue'),
        meta: { title: '產品管理' }
      },
      {
        path: 'products/create',
        name: 'CreateProduct',
        component: () => import('../views/products/ProductForm.vue'),
        meta: { title: '創建產品' }
      },
      {
        path: 'products/edit/:id',
        name: 'EditProduct',
        component: () => import('../views/products/ProductForm.vue'),
        meta: { title: '編輯產品' }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { title: '登入' }
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

// 路由守衛
router.beforeEach((to, from, next) => {
  // 設置頁面標題
  document.title = `${to.meta.title} - 蒼藍工程公司管理系統`;
  
  // 檢查是否需要登入
  const isAuthenticated = localStorage.getItem('token');
  if (to.name !== 'Login' && !isAuthenticated) {
    next({ name: 'Login' });
  } else {
    next();
  }
});

export default router; 