<template>
  <div class="data-table">
    <!-- 工具欄 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchQuery"
          :placeholder="$t('common.search')"
          prefix-icon="Search"
          clearable
          style="width: 300px"
          @input="handleSearch"
        />
        <el-select
          v-if="filters.length > 0"
          v-model="filterValue"
          :placeholder="$t('common.filter')"
          clearable
          style="width: 200px; margin-left: 10px"
          @change="handleFilter"
        >
          <el-option
            v-for="filter in filters"
            :key="filter.value"
            :label="filter.label"
            :value="filter.value"
          />
        </el-select>
      </div>
      <div class="toolbar-right">
        <el-button
          v-if="showRefresh"
          type="primary"
          :icon="RefreshRight"
          @click="handleRefresh"
        >
          {{ $t('common.refresh') }}
        </el-button>
        <el-button
          v-if="showAdd"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          {{ $t('common.add') }}
        </el-button>
        <el-button
          v-if="showExport"
          type="success"
          :icon="Download"
          @click="handleExport"
        >
          {{ $t('common.export') }}
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      :loading="loading"
      stripe
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
      />
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :sortable="column.sortable"
        :align="column.align || 'left'"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template #default="{ row }">
          <!-- 自定義插槽 -->
          <slot
            v-if="column.slot"
            :name="column.slot"
            :row="row"
            :column="column"
          />
          <!-- 狀態標籤 -->
          <el-tag
            v-else-if="column.type === 'status'"
            :type="getStatusType(row[column.prop])"
            size="small"
          >
            {{ formatStatus(row[column.prop]) }}
          </el-tag>
          <!-- 貨幣格式 -->
          <span v-else-if="column.type === 'currency'">
            {{ formatCurrency(row[column.prop]) }}
          </span>
          <!-- 日期格式 -->
          <span v-else-if="column.type === 'date'">
            {{ formatDate(row[column.prop]) }}
          </span>
          <!-- 默認顯示 -->
          <span v-else>{{ row[column.prop] }}</span>
        </template>
      </el-table-column>
      
      <!-- 操作列 -->
      <el-table-column
        v-if="showActions"
        :label="$t('common.actions')"
        :width="actionWidth"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <slot name="actions" :row="row">
            <el-button
              v-if="actions.includes('view')"
              type="primary"
              size="small"
              link
              @click="handleView(row)"
            >
              {{ $t('common.view') }}
            </el-button>
            <el-button
              v-if="actions.includes('edit')"
              type="primary"
              size="small"
              link
              @click="handleEdit(row)"
            >
              {{ $t('common.edit') }}
            </el-button>
            <el-button
              v-if="actions.includes('delete')"
              type="danger"
              size="small"
              link
              @click="handleDelete(row)"
            >
              {{ $t('common.delete') }}
            </el-button>
          </slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分頁 -->
    <el-pagination
      v-if="showPagination"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="pageSizes"
      :small="small"
      :background="true"
      layout="total, sizes, prev, pager, next, jumper"
      class="table-pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, RefreshRight, Download } from '@element-plus/icons-vue';
import { formatCurrency, formatDate, debounce } from '@/utils';

const { t } = useI18n();

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  pageSize: {
    type: Number,
    default: 10
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  small: {
    type: Boolean,
    default: false
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  showSelection: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: true
  },
  actions: {
    type: Array,
    default: () => ['view', 'edit', 'delete']
  },
  actionWidth: {
    type: [String, Number],
    default: 150
  },
  filters: {
    type: Array,
    default: () => []
  },
  showRefresh: {
    type: Boolean,
    default: true
  },
  showAdd: {
    type: Boolean,
    default: true
  },
  showExport: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'refresh',
  'add',
  'view',
  'edit',
  'delete',
  'export',
  'search',
  'filter',
  'sort',
  'selection-change',
  'size-change',
  'current-change'
]);

// 響應式數據
const searchQuery = ref('');
const filterValue = ref('');
const tableData = computed(() => props.data);

// 搜索處理
const handleSearch = debounce((value) => {
  emit('search', value);
}, 300);

// 篩選處理
const handleFilter = (value) => {
  emit('filter', value);
};

// 事件處理
const handleRefresh = () => emit('refresh');
const handleAdd = () => emit('add');
const handleView = (row) => emit('view', row);
const handleEdit = (row) => emit('edit', row);
const handleExport = () => emit('export');

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      t('common.confirmDelete'),
      t('common.warning'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    );
    emit('delete', row);
  } catch {
    // 用戶取消刪除
  }
};

const handleSelectionChange = (selection) => {
  emit('selection-change', selection);
};

const handleSortChange = (sortInfo) => {
  emit('sort', sortInfo);
};

const handleSizeChange = (size) => {
  emit('size-change', size);
};

const handleCurrentChange = (page) => {
  emit('current-change', page);
};

// 狀態類型映射
const getStatusType = (status) => {
  const typeMap = {
    'active': 'success',
    'inactive': 'info',
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'draft': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  };
  return typeMap[status] || 'info';
};

// 狀態文本格式化
const formatStatus = (status) => {
  const statusMap = {
    'active': t('status.active'),
    'inactive': t('status.inactive'),
    'pending': t('status.pending'),
    'approved': t('status.approved'),
    'rejected': t('status.rejected'),
    'draft': t('status.draft'),
    'completed': t('status.completed'),
    'cancelled': t('status.cancelled')
  };
  return statusMap[status] || status;
};
</script>

<style scoped>
.data-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.el-table {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-pagination {
  margin-top: 16px;
  text-align: right;
}
</style> 