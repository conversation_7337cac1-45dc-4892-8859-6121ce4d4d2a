<template>
  <div class="delivery-note-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>送貨單管理</span>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            創建送貨單
          </el-button>
        </div>
      </template>

      <!-- 搜索和篩選 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input
              v-model="searchForm.search"
              placeholder="搜索送貨單號或客戶名稱"
              clearable>
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="12">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 數據表格 -->
      <el-table :data="deliveryNotes" v-loading="loading" style="width: 100%">
        <el-table-column prop="delivery_note_no" label="送貨單號" width="150" />
        <el-table-column prop="customer_name" label="客戶" width="200" />
        <el-table-column prop="delivery_date" label="送貨日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.delivery_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="狀態" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="signature_status" label="簽收狀態" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.signature_status ? 'success' : 'warning'">
              {{ scope.row.signature_status ? '已簽收' : '未簽收' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="success" size="small" @click="handleEdit(scope.row)">
              編輯
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search } from '@element-plus/icons-vue';
import dayjs from 'dayjs';

const router = useRouter();

// 數據狀態
const deliveryNotes = ref([]);
const loading = ref(false);
const showCreateDialog = ref(false);

// 分頁狀態
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索表單
const searchForm = reactive({
  search: ''
});

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD');
};

// 狀態類型映射
const getStatusType = (status) => {
  const statusMap = {
    'draft': 'info',
    'pending': 'warning',
    'delivered': 'success',
    'cancelled': 'danger'
  };
  return statusMap[status] || 'info';
};

// 狀態文本映射
const getStatusText = (status) => {
  const statusMap = {
    'draft': '草稿',
    'pending': '待送貨',
    'delivered': '已送達',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// 獲取送貨單列表
const fetchDeliveryNotes = async () => {
  loading.value = true;
  try {
    // 模擬API調用
    const mockData = {
      deliveryNotes: [
        {
          id: 1,
          delivery_note_no: 'DN-202401-0001',
          customer_name: 'ABC電子有限公司',
          delivery_date: '2024-01-20',
          status: 'delivered',
          signature_status: true
        },
        {
          id: 2,
          delivery_note_no: 'DN-202401-0002',
          customer_name: 'XYZ工程公司',
          delivery_date: '2024-01-21',
          status: 'pending',
          signature_status: false
        }
      ],
      total: 2,
      page: 1,
      per_page: 10
    };

    deliveryNotes.value = mockData.deliveryNotes;
    total.value = mockData.total;
    
  } catch (error) {
    ElMessage.error('獲取送貨單列表失敗');
  } finally {
    loading.value = false;
  }
};

// 搜索處理
const handleSearch = () => {
  currentPage.value = 1;
  fetchDeliveryNotes();
};

// 重置搜索
const handleReset = () => {
  searchForm.search = '';
  currentPage.value = 1;
  fetchDeliveryNotes();
};

// 分頁處理
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchDeliveryNotes();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchDeliveryNotes();
};

// 查看送貨單
const handleView = (row) => {
  router.push(`/delivery-notes/${row.id}`);
};

// 編輯送貨單
const handleEdit = (row) => {
  router.push(`/delivery-notes/${row.id}/edit`);
};

// 刪除送貨單
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除送貨單 ${row.delivery_note_no} 嗎？此操作不可恢復。`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    ElMessage.success('刪除成功');
    fetchDeliveryNotes();
  } catch {
    // 用戶取消
  }
};

onMounted(() => {
  fetchDeliveryNotes();
});
</script>

<style scoped>
.delivery-note-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 