# 蒼藍工程公司管理系統 - 快速開始指南 v2.0
## H.B Engineering Company Management System - Quick Start Guide v2.0

本指南將幫助您快速部署和運行蒼藍工程公司管理系統的增強版本。

## 📋 前提條件

### 硬體需求
- **樹莓派**: Raspberry Pi 4B 4GB+ RAM（推薦）
- **儲存**: 32GB+ microSD卡（Class 10）
- **網絡**: 有線或WiFi連接

### 軟體需求
- **作業系統**: Raspberry Pi OS (64-bit) 或 Ubuntu Server 20.04+
- **Python**: 3.8+ (通常已預裝)
- **網絡瀏覽器**: 用於訪問管理界面（前端）

## 🚀 自動安裝（推薦）

### 步驟 1: 下載並運行安裝腳本

```bash
# 下載系統檔案到樹莓派
# 假設您已經將檔案上傳到 /opt/hb-engineering

cd /opt/hb-engineering

# 執行自動安裝腳本
chmod +x install.sh
sudo ./install.sh
```

安裝腳本將自動：
- 更新系統套件
- 安裝Python依賴
- 設置虛擬環境
- 初始化數據庫
- 配置系統服務
- 設置防火牆
- 創建備份任務
- 設置監控腳本

### 步驟 2: 驗證安裝

```bash
# 檢查服務狀態
sudo systemctl status hb-engineering

# 測試API端點
curl http://localhost:5000/api/system/health

# 查看日誌
sudo journalctl -u hb-engineering -f

# 測試新功能
curl http://localhost:5000/api/system/info
```

## 📱 手動安裝

如果自動安裝失敗，可以手動執行以下步驟：

### 步驟 1: 系統準備

```bash
# 更新系統
sudo apt update && sudo apt upgrade -y

# 安裝必要套件
sudo apt install python3 python3-pip python3-venv git nginx sqlite3 -y
```

### 步驟 2: 創建應用目錄

```bash
# 創建應用目錄
sudo mkdir -p /opt/hb-engineering
sudo chown $USER:$USER /opt/hb-engineering
cd /opt/hb-engineering

# 上傳或複製應用檔案到此目錄
```

### 步驟 3: 設置Python環境

```bash
# 創建虛擬環境
python3 -m venv venv
source venv/bin/activate

# 升級pip
pip install --upgrade pip

# 安裝依賴
pip install -r requirements.txt
```

### 步驟 4: 初始化數據庫

```bash
# 運行應用一次以初始化數據庫
python app.py &
sleep 10
kill %1

# 檢查數據庫是否創建成功
ls -la database/
sqlite3 database/hb_engineering.db ".tables"
```

### 步驟 5: 配置系統服務

```bash
# 創建systemd服務檔案
sudo tee /etc/systemd/system/hb-engineering.service > /dev/null <<EOF
[Unit]
Description=H.B Engineering Management System v2.0
After=network.target

[Service]
Type=simple
User=pi
WorkingDirectory=/opt/hb-engineering
Environment=PATH=/opt/hb-engineering/venv/bin
ExecStart=/opt/hb-engineering/venv/bin/python app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 啟用並啟動服務
sudo systemctl daemon-reload
sudo systemctl enable hb-engineering.service
sudo systemctl start hb-engineering.service
```

## ⚙️ 新功能配置

### 設置監控系統

```bash
# 創建監控配置
cat > monitor_config.json << EOF
{
  "app_url": "http://localhost:5000",
  "check_interval": 60,
  "thresholds": {
    "cpu_percent": 80,
    "memory_percent": 85,
    "disk_percent": 90,
    "response_time": 5.0
  },
  "email": {
    "enabled": false,
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "",
    "password": "",
    "to_email": "",
    "from_email": ""
  }
}
EOF

# 測試監控腳本
python monitor.py --once
```

### 設置自動備份

```bash
# 創建備份腳本
sudo tee /usr/local/bin/hb-backup.sh > /dev/null <<EOF
#!/bin/bash
cd /opt/hb-engineering
source venv/bin/activate
python -c "
import requests
response = requests.post('http://localhost:5000/api/backup/create', 
    headers={'Authorization': 'Bearer YOUR_ADMIN_TOKEN'})
print(response.json())
"
EOF

sudo chmod +x /usr/local/bin/hb-backup.sh

# 設置定時任務（每日2點備份）
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/hb-backup.sh") | crontab -
```

## 🌐 訪問系統

### 後端API
- **URL**: `http://樹莓派IP:5000`
- **健康檢查**: `http://樹莓派IP:5000/api/system/health`
- **系統資訊**: `http://樹莓派IP:5000/api/system/info`
- **API文檔**: 參考 `API_DOCUMENTATION.md` 和 `API_ENHANCEMENT.md`

### 默認管理員帳號
- **用戶名**: `admin`
- **密碼**: `admin123`

⚠️ **重要**: 首次登入後請立即更改默認密碼！

## 🔧 常用管理命令

```bash
# 查看服務狀態
sudo systemctl status hb-engineering

# 重啟服務
sudo systemctl restart hb-engineering

# 查看實時日誌
sudo journalctl -u hb-engineering -f

# 停止服務
sudo systemctl stop hb-engineering

# 啟動服務
sudo systemctl start hb-engineering

# 查看服務配置
sudo systemctl cat hb-engineering

# 運行監控檢查
cd /opt/hb-engineering && python monitor.py --once

# 手動創建備份
cd /opt/hb-engineering && curl -X POST http://localhost:5000/api/backup/create \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 測試新功能

### 1. 測試基本API

```bash
# 健康檢查
curl http://localhost:5000/api/system/health

# 系統資訊
curl http://localhost:5000/api/system/info

# 登入取得token
TOKEN=$(curl -s -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}' | \
  grep -o '"access_token":"[^"]*' | grep -o '[^"]*$')

echo "Token: $TOKEN"
```

### 2. 測試發票功能

```bash
# 獲取發票列表
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/invoices

# 創建測試客戶
curl -X POST http://localhost:5000/api/customers \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "測試公司", "contact_person": "張先生", "phone": "2123-4567"}'

# 創建測試產品
curl -X POST http://localhost:5000/api/products \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "電力安裝服務", "unit_price": 1000.0, "unit": "項"}'
```

### 3. 測試統計功能

```bash
# 獲取儀表板統計
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/statistics/dashboard

# 獲取收入統計
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/statistics/revenue
```

### 4. 測試搜索功能

```bash
# 全局搜索
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:5000/api/search?q=測試&type=all"
```

### 5. 測試備份功能

```bash
# 創建備份
curl -X POST http://localhost:5000/api/backup/create \
  -H "Authorization: Bearer $TOKEN"

# 列出備份
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/backup/list
```

## 🐛 故障排除

### 常見問題

1. **服務無法啟動**
   ```bash
   # 查看詳細錯誤
   sudo journalctl -u hb-engineering -n 50
   
   # 檢查Python依賴
   cd /opt/hb-engineering
   source venv/bin/activate
   python -c "import flask, flask_cors, flask_jwt_extended, reportlab; print('依賴正常')"
   ```

2. **數據庫錯誤**
   ```bash
   # 檢查數據庫檔案
   ls -la /opt/hb-engineering/database/
   
   # 檢查數據庫完整性
   sqlite3 database/hb_engineering.db "PRAGMA integrity_check;"
   ```

3. **新功能不工作**
   ```bash
   # 檢查API版本
   curl http://localhost:5000/api/system/info
   
   # 檢查所有路由
   cd /opt/hb-engineering
   source venv/bin/activate
   python -c "from app import app; print('\n'.join([str(rule) for rule in app.url_map.iter_rules()]))"
   ```

4. **權限問題**
   ```bash
   # 修復檔案權限
   sudo chown -R pi:pi /opt/hb-engineering/
   sudo chmod +x /opt/hb-engineering/app.py
   ```

### 效能調優

```bash
# 檢查系統資源
free -h
df -h
top

# 檢查應用效能
curl -w "@-" -o /dev/null -s http://localhost:5000/api/system/health <<< "
     時間_名稱解析:  %{time_namelookup}\n
       時間_連接:  %{time_connect}\n
    時間_應用連接:  %{time_appconnect}\n
   時間_預傳輸:  %{time_pretransfer}\n
      時間_重定向:  %{time_redirect}\n
   時間_開始傳輸:  %{time_starttransfer}\n
                     ----------\n
        時間_總計:  %{time_total}\n
"
```

## 📈 下一步

安裝完成後，您可以：

1. **更改默認密碼**
   ```bash
   curl -X PUT http://localhost:5000/api/users/1 \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"password": "new_secure_password"}'
   ```

2. **創建其他用戶帳號**
   ```bash
   curl -X POST http://localhost:5000/api/auth/register \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"username": "user1", "password": "password123", "email": "<EMAIL>"}'
   ```

3. **設置公司資訊**
   ```bash
   curl -X PUT http://localhost:5000/api/settings \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"company_name": "您的公司名稱", "company_address": "公司地址"}'
   ```

4. **配置監控和備份**
5. **安裝和配置前端應用**
6. **開始使用新功能（發票、送貨單、統計等）**

## 🆕 v2.0 新功能亮點

- ✅ **完整的發票管理系統** - 創建、查看、PDF生成
- ✅ **完整的送貨單管理系統** - 送貨跟蹤、簽收管理
- ✅ **文檔轉換功能** - 報價單→發票→送貨單
- ✅ **統計分析儀表板** - 實時業務數據統計
- ✅ **系統監控** - 健康檢查、性能監控
- ✅ **數據備份系統** - 自動備份、手動備份
- ✅ **全局搜索** - 跨模塊快速搜索
- ✅ **文件上傳** - 支持文檔和圖片上傳
- ✅ **增強的用戶管理** - 完整的CRUD操作
- ✅ **產品管理完善** - 支持產品生命周期管理

## 📞 支援

如果遇到問題：

1. 查看 `README.md` 的詳細說明
2. 參考 `RASPBERRY_PI_SETUP.md` 的完整部署指南
3. 檢查 `API_DOCUMENTATION.md` 的基本API說明
4. 查看 `API_ENHANCEMENT.md` 的新功能文檔
5. 聯繫技術支援: <EMAIL>

---

**最後更新**: 2023年12月15日
**版本**: 2.0.0
**新增功能**: 發票管理、送貨單管理、統計分析、系統監控、數據備份 