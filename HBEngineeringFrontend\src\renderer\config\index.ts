// 應用配置管理

interface AppConfig {
  // 應用基本信息
  app: {
    title: string;
    version: string;
    description: string;
  };
  
  // API 配置
  api: {
    baseURL: string;
    timeout: number;
  };
  
  // 後端服務配置
  backend: {
    host: string;
    port: number;
  };
  
  // 安全配置
  security: {
    encryptionKey: string;
    jwtSecret: string;
    csrfTokenName: string;
    enableCSP: boolean;
    enableHTTPSOnly: boolean;
    enableHSTS: boolean;
    rateLimitRequests: number;
    rateLimitWindow: number;
  };
  
  // 功能開關
  features: {
    enableMock: boolean;
    enableDebug: boolean;
    enableAnalytics: boolean;
    enableErrorReporting: boolean;
    enablePWA: boolean;
    enableCompression: boolean;
    enableLazyLoading: boolean;
  };
  
  // 文件上傳配置
  upload: {
    maxFileSize: number;
    allowedFileTypes: string[];
  };
  
  // 系統配置
  system: {
    sessionTimeout: number;
    autoLogoutWarning: number;
    maintenanceMode: boolean;
  };
  
  // 開發配置
  dev: {
    serverPort: number;
    serverHost: string;
    openBrowser: boolean;
  };
  
  // 構建配置
  build: {
    sourcemap: boolean;
    minify: boolean;
    analyze: boolean;
  };
  
  // 日誌配置
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    toConsole: boolean;
    toFile: boolean;
  };
  
  // 緩存配置
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
  
  // 國際化配置
  i18n: {
    defaultLocale: string;
    fallbackLocale: string;
    availableLocales: string[];
  };
  
  // 主題配置
  theme: {
    defaultTheme: 'light' | 'dark';
    themeColor: string;
    enableDarkMode: boolean;
  };
  
  // 性能配置
  performance: {
    virtualScrollThreshold: number;
  };
  
  // 第三方服務配置
  services: {
    sentryDSN?: string;
    googleAnalyticsId?: string;
    hotjarId?: string;
  };
}

// 獲取環境變量值，支持類型轉換和默認值
function getEnvValue<T>(key: string, defaultValue: T, transform?: (value: string) => T): T {
  const value = import.meta.env[key];
  
  if (value === undefined || value === '') {
    return defaultValue;
  }
  
  if (transform) {
    try {
      return transform(value);
    } catch {
      return defaultValue;
    }
  }
  
  // 自動類型轉換
  if (typeof defaultValue === 'boolean') {
    return (value.toLowerCase() === 'true') as T;
  }
  
  if (typeof defaultValue === 'number') {
    const num = Number(value);
    return (isNaN(num) ? defaultValue : num) as T;
  }
  
  if (Array.isArray(defaultValue)) {
    return value.split(',').map(item => item.trim()) as T;
  }
  
  return value as T;
}

// 創建應用配置
export const config: AppConfig = {
  app: {
    title: getEnvValue('VITE_APP_TITLE', '蒼藍工程公司管理系統'),
    version: getEnvValue('VITE_APP_VERSION', '2.0.0'),
    description: getEnvValue('VITE_APP_DESCRIPTION', '現代化企業管理平台'),
  },
  
  api: {
    baseURL: getEnvValue('VITE_API_BASE_URL', '/api'),
    timeout: getEnvValue('VITE_API_TIMEOUT', 30000),
  },
  
  backend: {
    host: getEnvValue('VITE_BACKEND_HOST', 'localhost'),
    port: getEnvValue('VITE_BACKEND_PORT', 5000),
  },
  
  security: {
    encryptionKey: getEnvValue('VITE_ENCRYPTION_KEY', 'default-encryption-key'),
    jwtSecret: getEnvValue('VITE_JWT_SECRET', 'default-jwt-secret'),
    csrfTokenName: getEnvValue('VITE_CSRF_TOKEN_NAME', '_csrf_token'),
    enableCSP: getEnvValue('VITE_ENABLE_CSP', true),
    enableHTTPSOnly: getEnvValue('VITE_ENABLE_HTTPS_ONLY', false),
    enableHSTS: getEnvValue('VITE_ENABLE_HSTS', false),
    rateLimitRequests: getEnvValue('VITE_RATE_LIMIT_REQUESTS', 100),
    rateLimitWindow: getEnvValue('VITE_RATE_LIMIT_WINDOW', 60000),
  },
  
  features: {
    enableMock: getEnvValue('VITE_ENABLE_MOCK', false),
    enableDebug: getEnvValue('VITE_ENABLE_DEBUG', false),
    enableAnalytics: getEnvValue('VITE_ENABLE_ANALYTICS', false),
    enableErrorReporting: getEnvValue('VITE_ENABLE_ERROR_REPORTING', false),
    enablePWA: getEnvValue('VITE_ENABLE_PWA', false),
    enableCompression: getEnvValue('VITE_ENABLE_COMPRESSION', true),
    enableLazyLoading: getEnvValue('VITE_ENABLE_LAZY_LOADING', true),
  },
  
  upload: {
    maxFileSize: getEnvValue('VITE_MAX_FILE_SIZE', 10485760), // 10MB
    allowedFileTypes: getEnvValue('VITE_ALLOWED_FILE_TYPES', [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf'
    ]),
  },
  
  system: {
    sessionTimeout: getEnvValue('VITE_SESSION_TIMEOUT', 1800000), // 30分鐘
    autoLogoutWarning: getEnvValue('VITE_AUTO_LOGOUT_WARNING', 300000), // 5分鐘
    maintenanceMode: getEnvValue('VITE_MAINTENANCE_MODE', false),
  },
  
  dev: {
    serverPort: getEnvValue('VITE_DEV_SERVER_PORT', 3000),
    serverHost: getEnvValue('VITE_DEV_SERVER_HOST', 'localhost'),
    openBrowser: getEnvValue('VITE_DEV_OPEN_BROWSER', true),
  },
  
  build: {
    sourcemap: getEnvValue('VITE_BUILD_SOURCEMAP', false),
    minify: getEnvValue('VITE_BUILD_MINIFY', true),
    analyze: getEnvValue('VITE_BUILD_ANALYZE', false),
  },
  
  logging: {
    level: getEnvValue('VITE_LOG_LEVEL', 'info') as 'debug' | 'info' | 'warn' | 'error',
    toConsole: getEnvValue('VITE_LOG_TO_CONSOLE', true),
    toFile: getEnvValue('VITE_LOG_TO_FILE', false),
  },
  
  cache: {
    enabled: getEnvValue('VITE_CACHE_ENABLED', true),
    ttl: getEnvValue('VITE_CACHE_TTL', 300000), // 5分鐘
    maxSize: getEnvValue('VITE_CACHE_MAX_SIZE', 100),
  },
  
  i18n: {
    defaultLocale: getEnvValue('VITE_DEFAULT_LOCALE', 'zh-HK'),
    fallbackLocale: getEnvValue('VITE_FALLBACK_LOCALE', 'en'),
    availableLocales: getEnvValue('VITE_AVAILABLE_LOCALES', ['zh-HK', 'en', 'zh-CN']),
  },
  
  theme: {
    defaultTheme: getEnvValue('VITE_DEFAULT_THEME', 'light') as 'light' | 'dark',
    themeColor: getEnvValue('VITE_THEME_COLOR', '#409EFF'),
    enableDarkMode: getEnvValue('VITE_ENABLE_DARK_MODE', true),
  },
  
  performance: {
    virtualScrollThreshold: getEnvValue('VITE_VIRTUAL_SCROLL_THRESHOLD', 100),
  },
  
  services: {
    sentryDSN: getEnvValue('VITE_SENTRY_DSN', ''),
    googleAnalyticsId: getEnvValue('VITE_GOOGLE_ANALYTICS_ID', ''),
    hotjarId: getEnvValue('VITE_HOTJAR_ID', ''),
  },
};

// 配置驗證
export function validateConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 檢查必需的配置項
  if (!config.app.title) {
    errors.push('應用標題不能為空');
  }
  
  if (!config.api.baseURL) {
    errors.push('API 基礎 URL 不能為空');
  }
  
  if (config.api.timeout <= 0) {
    errors.push('API 超時時間必須大於 0');
  }
  
  if (config.system.sessionTimeout <= 0) {
    errors.push('會話超時時間必須大於 0');
  }
  
  if (config.upload.maxFileSize <= 0) {
    errors.push('最大文件大小必須大於 0');
  }
  
  if (config.upload.allowedFileTypes.length === 0) {
    errors.push('必須指定至少一種允許的文件類型');
  }
  
  // 檢查安全配置
  if (import.meta.env.PROD) {
    if (config.security.encryptionKey === 'default-encryption-key') {
      errors.push('生產環境必須設置自定義加密密鑰');
    }
    
    if (config.security.jwtSecret === 'default-jwt-secret') {
      errors.push('生產環境必須設置自定義 JWT 密鑰');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// 獲取運行時配置
export function getRuntimeConfig() {
  return {
    isDevelopment: import.meta.env.DEV,
    isProduction: import.meta.env.PROD,
    mode: import.meta.env.MODE,
    baseUrl: import.meta.env.BASE_URL,
    timestamp: new Date().toISOString(),
  };
}

// 導出配置對象
export default config;
