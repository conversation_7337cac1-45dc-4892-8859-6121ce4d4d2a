<template>
  <div class="product-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>產品管理</span>
          <el-button type="primary" @click="showDialog = true">
            <el-icon><Plus /></el-icon>
            新增產品
          </el-button>
        </div>
      </template>

      <!-- 搜索篩選 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchForm.search"
              placeholder="搜索產品名稱或描述"
              clearable>
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.category" placeholder="產品分類" clearable>
              <el-option label="全部" value="" />
              <el-option label="照明設備" value="照明設備" />
              <el-option label="電線電纜" value="電線電纜" />
              <el-option label="開關插座" value="開關插座" />
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 數據表格 -->
      <el-table :data="products" v-loading="loading" style="width: 100%">
        <el-table-column prop="name" label="產品名稱" width="200" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="category" label="分類" width="120" />
        <el-table-column prop="unit" label="單位" width="80" />
        <el-table-column prop="unit_price" label="單價" width="120" align="right">
          <template #default="scope">
            ${{ scope.row.unit_price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="狀態" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_active ? 'success' : 'info'">
              {{ scope.row.is_active ? '啟用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              編輯
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/編輯對話框 -->
    <el-dialog
      v-model="showDialog"
      :title="isEdit ? '編輯產品' : '新增產品'"
      width="600px"
      @close="handleDialogClose">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px">
        <el-form-item label="產品名稱" prop="name">
          <el-input v-model="form.name" placeholder="請輸入產品名稱" />
        </el-form-item>
        <el-form-item label="產品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入產品描述" />
        </el-form-item>
        <el-form-item label="產品分類" prop="category">
          <el-select v-model="form.category" placeholder="請選擇產品分類">
            <el-option label="照明設備" value="照明設備" />
            <el-option label="電線電纜" value="電線電纜" />
            <el-option label="開關插座" value="開關插座" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="計量單位" prop="unit">
          <el-select v-model="form.unit" placeholder="請選擇計量單位">
            <el-option label="個" value="pc" />
            <el-option label="米" value="m" />
            <el-option label="公斤" value="kg" />
            <el-option label="套" value="set" />
          </el-select>
        </el-form-item>
        <el-form-item label="單價" prop="unit_price">
          <el-input-number
            v-model="form.unit_price"
            :precision="2"
            :min="0"
            placeholder="請輸入單價" />
        </el-form-item>
        <el-form-item label="是否啟用" prop="is_active">
          <el-switch v-model="form.is_active" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '創建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search } from '@element-plus/icons-vue';

// 數據狀態
const products = ref([]);
const loading = ref(false);
const submitting = ref(false);

// 分頁狀態
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索表單
const searchForm = reactive({
  search: '',
  category: ''
});

// 對話框狀態
const showDialog = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 表單數據
const form = reactive({
  id: null,
  name: '',
  description: '',
  category: '',
  unit: '',
  unit_price: 0,
  is_active: true
});

// 表單驗證規則
const rules = {
  name: [
    { required: true, message: '請輸入產品名稱', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '請選擇產品分類', trigger: 'change' }
  ],
  unit: [
    { required: true, message: '請選擇計量單位', trigger: 'change' }
  ],
  unit_price: [
    { required: true, message: '請輸入單價', trigger: 'blur' }
  ]
};

// 獲取產品列表
const fetchProducts = async () => {
  loading.value = true;
  try {
    // 模擬API調用
    const mockData = {
      products: [
        {
          id: 1,
          name: 'LED燈具',
          description: '高效能LED照明設備',
          category: '照明設備',
          unit: 'pc',
          unit_price: 150.00,
          is_active: true
        },
        {
          id: 2,
          name: '電纜線',
          description: '高品質電纜線材',
          category: '電線電纜',
          unit: 'm',
          unit_price: 25.50,
          is_active: true
        },
        {
          id: 3,
          name: '開關插座',
          description: '標準電器開關插座',
          category: '開關插座',
          unit: 'pc',
          unit_price: 35.00,
          is_active: false
        }
      ],
      total: 3,
      page: 1,
      per_page: 10
    };

    products.value = mockData.products;
    total.value = mockData.total;
    
  } catch (error) {
    ElMessage.error('獲取產品列表失敗');
  } finally {
    loading.value = false;
  }
};

// 搜索處理
const handleSearch = () => {
  currentPage.value = 1;
  fetchProducts();
};

// 重置搜索
const handleReset = () => {
  searchForm.search = '';
  searchForm.category = '';
  currentPage.value = 1;
  fetchProducts();
};

// 分頁處理
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchProducts();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchProducts();
};

// 編輯產品
const handleEdit = (row) => {
  isEdit.value = true;
  Object.assign(form, row);
  showDialog.value = true;
};

// 刪除產品
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除產品 ${row.name} 嗎？此操作不可恢復。`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    ElMessage.success('刪除成功');
    fetchProducts();
  } catch {
    // 用戶取消
  }
};

// 對話框關閉處理
const handleDialogClose = () => {
  isEdit.value = false;
  Object.assign(form, {
    id: null,
    name: '',
    description: '',
    category: '',
    unit: '',
    unit_price: 0,
    is_active: true
  });
  formRef.value?.resetFields();
};

// 提交表單
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    submitting.value = true;
    
    // 模擬API調用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success(isEdit.value ? '更新成功' : '創建成功');
    showDialog.value = false;
    fetchProducts();
    
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message);
    }
  } finally {
    submitting.value = false;
  }
};

onMounted(() => {
  fetchProducts();
});
</script>

<style scoped>
.product-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 