import axios from 'axios';
import { ElMessage } from 'element-plus';

// 創建axios實例
const apiClient = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 請求攔截器
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 響應攔截器
apiClient.interceptors.response.use(
  response => {
    const { data } = response;
    if (data.success === false) {
      ElMessage.error(data.error || '請求失敗');
      return Promise.reject(new Error(data.error || '請求失敗'));
    }
    return data;
  },
  error => {
    let message = '網絡錯誤';
    
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          message = '未授權，請重新登入';
          localStorage.removeItem('token');
          localStorage.removeItem('userInfo');
          window.location.href = '#/login';
          break;
        case 403:
          message = '沒有權限';
          break;
        case 404:
          message = '資源不存在';
          break;
        case 500:
          message = '服務器錯誤';
          break;
        default:
          message = data?.error || `請求失敗 (${status})`;
      }
    } else if (error.request) {
      message = '網絡連接失敗';
    }
    
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

export default apiClient; 