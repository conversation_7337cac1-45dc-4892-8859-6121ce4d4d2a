# 🔌 API 接口文檔

## 📋 目錄
- [認證接口](#認證接口)
- [報價單接口](#報價單接口)
- [客戶接口](#客戶接口)
- [產品接口](#產品接口)
- [發票接口](#發票接口)
- [送貨單接口](#送貨單接口)
- [文件接口](#文件接口)
- [系統接口](#系統接口)

## 🔐 認證接口

### 登入
```http
POST /api/auth/login
```

**請求參數:**
```json
{
  "username": "string",
  "password": "string",
  "remember": "boolean"
}
```

**響應:**
```json
{
  "success": true,
  "data": {
    "access_token": "string",
    "refresh_token": "string",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "username": "admin",
      "name": "管理員",
      "email": "<EMAIL>",
      "role": "admin",
      "status": "active"
    },
    "permissions": [
      {
        "id": 1,
        "code": "user.view",
        "name": "查看用戶",
        "module": "user",
        "enabled": true
      }
    ]
  }
}
```

### 登出
```http
POST /api/auth/logout
```

**請求頭:**
```
Authorization: Bearer {access_token}
```

**響應:**
```json
{
  "success": true,
  "message": "登出成功"
}
```

### 刷新 Token
```http
POST /api/auth/refresh
```

**請求參數:**
```json
{
  "refresh_token": "string"
}
```

**響應:**
```json
{
  "success": true,
  "data": {
    "access_token": "string",
    "expires_in": 3600
  }
}
```

### 獲取用戶信息
```http
GET /api/auth/me
```

**請求頭:**
```
Authorization: Bearer {access_token}
```

**響應:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "name": "管理員",
      "email": "<EMAIL>",
      "role": "admin",
      "status": "active",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    "permissions": []
  }
}
```

## 📋 報價單接口

### 獲取報價單列表
```http
GET /api/quotations
```

**查詢參數:**
```
page=1&size=20&search=&status=&customer_id=&date_from=&date_to=
```

**響應:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "quotation_no": "QT-202401-0001",
        "customer_id": 1,
        "customer_name": "ABC 公司",
        "project_name": "辦公樓電力工程",
        "status": "draft",
        "final_amount": 50000.00,
        "valid_until": "2024-12-31",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "size": 20,
    "pages": 5
  }
}
```

### 獲取報價單詳情
```http
GET /api/quotations/{id}
```

**響應:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "quotation_no": "QT-202401-0001",
    "customer_id": 1,
    "customer_name": "ABC 公司",
    "customer_contact": "張先生",
    "customer_phone": "12345678",
    "customer_email": "<EMAIL>",
    "customer_address": "香港中環...",
    "project_name": "辦公樓電力工程",
    "project_location": "香港中環",
    "description": "項目描述",
    "status": "draft",
    "subtotal_amount": 45000.00,
    "discount_amount": 0.00,
    "tax_amount": 0.00,
    "final_amount": 45000.00,
    "valid_from": "2024-01-01",
    "valid_until": "2024-12-31",
    "payment_terms": "款到發貨",
    "delivery_terms": "送貨上門",
    "warranty_terms": "一年保修",
    "items": [
      {
        "id": 1,
        "product_name": "電線",
        "specification": "2.5mm²",
        "unit": "米",
        "quantity": 100,
        "unit_price": 10.00,
        "total_price": 1000.00
      }
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 創建報價單
```http
POST /api/quotations
```

**請求參數:**
```json
{
  "customer_id": 1,
  "project_name": "辦公樓電力工程",
  "project_location": "香港中環",
  "description": "項目描述",
  "valid_until": "2024-12-31",
  "payment_terms": "款到發貨",
  "delivery_terms": "送貨上門",
  "warranty_terms": "一年保修",
  "discount_rate": 0,
  "tax_rate": 0,
  "items": [
    {
      "product_name": "電線",
      "specification": "2.5mm²",
      "unit": "米",
      "quantity": 100,
      "unit_price": 10.00
    }
  ]
}
```

### 更新報價單
```http
PUT /api/quotations/{id}
```

### 刪除報價單
```http
DELETE /api/quotations/{id}
```

### 複製報價單
```http
POST /api/quotations/{id}/duplicate
```

### 轉換為發票
```http
POST /api/quotations/{id}/convert-to-invoice
```

### 轉換為送貨單
```http
POST /api/quotations/{id}/convert-to-delivery-note
```

### 生成 PDF
```http
GET /api/quotations/{id}/pdf
```

### 發送郵件
```http
POST /api/quotations/{id}/send-email
```

**請求參數:**
```json
{
  "to": "<EMAIL>",
  "subject": "報價單",
  "content": "郵件內容"
}
```

## 👥 客戶接口

### 獲取客戶列表
```http
GET /api/customers
```

**查詢參數:**
```
page=1&size=20&search=&type=&status=
```

**響應:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "ABC 公司",
        "type": "company",
        "contact_person": "張先生",
        "phone": "12345678",
        "email": "<EMAIL>",
        "address": "香港中環...",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 50
  }
}
```

### 創建客戶
```http
POST /api/customers
```

**請求參數:**
```json
{
  "name": "ABC 公司",
  "type": "company",
  "contact_person": "張先生",
  "phone": "12345678",
  "email": "<EMAIL>",
  "address": "香港中環...",
  "remarks": "備註"
}
```

### 搜索客戶
```http
GET /api/customers/search?q={query}
```

## 📦 產品接口

### 獲取產品列表
```http
GET /api/products
```

**響應:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "電線",
        "code": "WIRE001",
        "category": "電線電纜",
        "specification": "2.5mm²",
        "unit": "米",
        "price": 10.00,
        "stock": 1000,
        "min_stock": 100,
        "status": "active"
      }
    ]
  }
}
```

### 獲取產品分類
```http
GET /api/products/categories
```

## 📄 發票接口

### 獲取發票列表
```http
GET /api/invoices
```

### 創建發票
```http
POST /api/invoices
```

### 生成發票 PDF
```http
GET /api/invoices/{id}/pdf
```

## 🚚 送貨單接口

### 獲取送貨單列表
```http
GET /api/delivery-notes
```

### 創建送貨單
```http
POST /api/delivery-notes
```

### 生成送貨單 PDF
```http
GET /api/delivery-notes/{id}/pdf
```

## 📁 文件接口

### 上傳文件
```http
POST /api/files/upload
```

**請求類型:** `multipart/form-data`

**請求參數:**
```
file: File
```

**響應:**
```json
{
  "success": true,
  "data": {
    "url": "/uploads/filename.jpg",
    "filename": "filename.jpg",
    "size": 1024,
    "type": "image/jpeg"
  }
}
```

### 刪除文件
```http
DELETE /api/files/{filename}
```

## ⚙️ 系統接口

### 獲取系統信息
```http
GET /api/system/info
```

**響應:**
```json
{
  "success": true,
  "data": {
    "version": "2.0.0",
    "build": "20240101",
    "environment": "production",
    "uptime": 86400,
    "database": {
      "status": "connected",
      "version": "3.39.0"
    }
  }
}
```

### 健康檢查
```http
GET /api/system/health
```

**響應:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00Z",
    "checks": {
      "database": "ok",
      "storage": "ok",
      "memory": "ok"
    }
  }
}
```

### 備份數據
```http
GET /api/system/backup
```

## 📝 錯誤響應格式

所有 API 錯誤響應都遵循統一格式：

```json
{
  "success": false,
  "error": "錯誤信息",
  "code": "ERROR_CODE",
  "details": {
    "field": "具體錯誤詳情"
  }
}
```

### 常見錯誤碼

| 狀態碼 | 錯誤碼 | 說明 |
|--------|--------|------|
| 400 | INVALID_REQUEST | 請求參數錯誤 |
| 401 | UNAUTHORIZED | 未授權 |
| 403 | FORBIDDEN | 沒有權限 |
| 404 | NOT_FOUND | 資源不存在 |
| 422 | VALIDATION_ERROR | 數據驗證失敗 |
| 429 | RATE_LIMIT_EXCEEDED | 請求過於頻繁 |
| 500 | INTERNAL_ERROR | 服務器內部錯誤 |

## 🔒 認證說明

### JWT Token 使用

1. **獲取 Token**: 通過登入接口獲取 `access_token`
2. **使用 Token**: 在請求頭中添加 `Authorization: Bearer {access_token}`
3. **Token 過期**: 使用 `refresh_token` 刷新 Token
4. **Token 失效**: 重新登入獲取新 Token

### 權限控制

API 接口根據用戶角色和權限進行訪問控制：

- **super_admin**: 所有權限
- **admin**: 管理權限（除系統設置外）
- **manager**: 業務管理權限
- **employee**: 基本操作權限
- **viewer**: 只讀權限

## 📊 分頁說明

列表接口支持分頁查詢：

**查詢參數:**
- `page`: 頁碼（從 1 開始）
- `size`: 每頁數量（默認 20，最大 100）

**響應格式:**
```json
{
  "success": true,
  "data": {
    "items": [],
    "total": 100,
    "page": 1,
    "size": 20,
    "pages": 5
  }
}
```

## 🔍 搜索和篩選

支持多種搜索和篩選方式：

- **關鍵字搜索**: `search` 參數
- **狀態篩選**: `status` 參數
- **日期範圍**: `date_from` 和 `date_to` 參數
- **排序**: `sort_by` 和 `sort_order` 參數

## 📈 速率限制

為防止濫用，API 實施速率限制：

- **一般接口**: 每分鐘 100 次請求
- **登入接口**: 每分鐘 10 次請求
- **文件上傳**: 每分鐘 20 次請求

超出限制將返回 `429 Too Many Requests` 錯誤。
