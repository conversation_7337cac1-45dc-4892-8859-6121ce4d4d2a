# 蒼藍工程公司管理系統 (前後端分離版本)
## H.B Engineering Company Management System

### 系統概述

這是一個專為「蒼藍工程公司」(H.B ENGINEERING COMPANY) 開發的現代化管理系統，採用 **前後端分離架構**。後端使用 **Python Flask** 構建RESTful API服務，運行在 **樹莓派** 上；前端使用 **Electron** 構建跨平台桌面應用程式。系統集成了報價單、發票、送貨單的管理功能，支援中英文雙語界面，專為香港電力工程公司的業務需求而設計。

### 主要功能

#### 📋 核心業務模塊
- **報價單管理 (Quotation Management)**
  - 自動編號生成 (格式: QT-YYYYMM-XXXX)
  - 產品選擇與自定義項目輸入
  - 價格計算與折扣管理
  - 轉換為發票或送貨單

- **發票管理 (Invoice Management)**
  - 自動編號生成 (格式: INV-YYYYMM-XXXX)
  - 從報價單轉換或獨立創建
  - 付款狀態追蹤
  - 稅額計算

- **送貨單管理 (Delivery Note Management)**
  - 自動編號生成 (格式: DN-YYYYMM-XXXX)
  - 送貨狀態追蹤
  - 電子簽收功能

#### 👥 數據管理
- **客戶管理**: 完整的客戶資料庫
- **產品管理**: 產品規格、價格管理
- **用戶管理**: 多用戶權限控制
- **文件管理**: 相關文檔和圖片上傳

#### 🎨 用戶界面
- **現代化設計**: 簡潔直觀的 Material Design 風格
- **中英文切換**: 完整的國際化支援
- **響應式佈局**: 適應不同螢幕尺寸
- **深色/淺色主題**: 個人化外觀設定

#### 📄 文檔生成
- **PDF 生成**: 專業格式的報價單、發票、送貨單
- **打印功能**: 直接打印或儲存為 PDF
- **預覽功能**: 即時預覽文檔效果
- **範本自定義**: 可自定義文檔範本

### 技術架構

#### 後端技術棧 (樹莓派)
- **程式語言**: Python 3.8+
- **Web框架**: Flask 2.3.3
- **API架構**: RESTful API
- **數據庫**: SQLite (輕量級、檔案型)
- **認證**: JWT (JSON Web Tokens)
- **文檔生成**: ReportLab (PDF)
- **部署平台**: Raspberry Pi OS / Ubuntu Server

#### 前端技術棧
- **框架**: Electron (跨平台桌面應用)
- **支援語言**: JavaScript/TypeScript
- **界面**: HTML5 + CSS3 + JavaScript
- **平台支援**: Windows, macOS, Linux

#### 系統架構
```
┌─────────────────────────────────────────────────┐
│                Electron 前端                     │
│            (桌面應用程式界面)                      │
└─────────────────┬───────────────────────────────┘
                  │ HTTP/HTTPS API 調用
                  │
┌─────────────────▼───────────────────────────────┐
│              樹莓派後端服務                        │
│  ┌─────────────────┐  ┌─────────────────────┐   │
│  │   Flask API     │  │    業務邏輯層        │   │
│  │   (RESTful)     │  │  (報價單/發票/送貨)   │   │
│  └─────────────────┘  └─────────────────────┘   │
│  ┌─────────────────┐  ┌─────────────────────┐   │
│  │   數據存取層     │  │     SQLite         │   │
│  │  (ORM/Query)    │  │     數據庫          │   │
│  └─────────────────┘  └─────────────────────┘   │
└─────────────────────────────────────────────────┘
```

#### 後端目錄結構 (樹莓派)
```
HBEngineeringBackend/
├── app.py                      # Flask 應用程式主文件
├── config.py                   # 配置管理
├── requirements.txt            # Python 依賴清單
├── .env                        # 環境變數配置
├── README.md                   # 說明文件
├── API_DOCUMENTATION.md        # API 文檔
├── RASPBERRY_PI_SETUP.md       # 樹莓派部署指南
│
├── database/                   # 數據庫目錄
│   └── hb_engineering.db       # SQLite 數據庫文件
│
├── logs/                       # 日誌目錄
│   └── app.log                 # 應用程式日誌
│
├── uploads/                    # 上傳檔案目錄
│   └── signatures/             # 簽名檔案
│
├── exports/                    # 匯出檔案目錄
│   └── pdf/                    # PDF 文件
│
├── backups/                    # 備份目錄
│   └── *.db                    # 數據庫備份
│
├── scripts/                    # 腳本目錄
│   ├── backup.sh               # 備份腳本
│   ├── start_backend.sh        # 啟動腳本
│   └── monitor.sh              # 監控腳本
│
└── systemd/                    # 系統服務配置
    └── hb-engineering.service  # systemd 服務文件
```

#### 前端目錄結構 (Electron)
```
HBEngineeringFrontend/
├── main.js                     # Electron 主程序
├── package.json                # Node.js 依賴配置
├── package-lock.json           # 依賴鎖定文件
├── README.md                   # 前端說明文件
│
├── src/                        # 源代碼目錄
│   ├── renderer/               # 渲染進程
│   │   ├── index.html          # 主頁面
│   │   ├── styles/             # 樣式表
│   │   ├── scripts/            # JavaScript 腳本
│   │   └── components/         # UI 組件
│   │
│   ├── api/                    # API 調用模塊
│   │   ├── client.js           # API 客戶端
│   │   └── endpoints.js        # API 端點定義
│   │
│   └── utils/                  # 工具函數
│       ├── auth.js             # 認證工具
│       └── helpers.js          # 輔助函數
│
├── assets/                     # 資源文件
│   ├── icons/                  # 應用程式圖標
│   ├── images/                 # 圖片資源
│   └── fonts/                  # 字體文件
│
├── locales/                    # 國際化文件
│   ├── zh-HK.json              # 繁體中文
│   └── en.json                 # 英文
│
└── build/                      # 構建輸出目錄
    ├── win32/                  # Windows 版本
    ├── darwin/                 # macOS 版本
    └── linux/                  # Linux 版本
```

### 安裝與部署

#### 後端環境需求 (樹莓派)
- **硬體**: Raspberry Pi 4B 4GB+ RAM
- **作業系統**: Raspberry Pi OS (64-bit) 或 Ubuntu Server 20.04+
- **Python**: 3.8+
- **儲存**: 32GB+ microSD卡

#### 前端環境需求
- **作業系統**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Node.js**: 16.0+
- **記憶體**: 4GB+ RAM

#### 後端部署步驟 (樹莓派)

1. **準備樹莓派環境**
   ```bash
   sudo apt update && sudo apt upgrade -y
   sudo apt install python3 python3-pip python3-venv git -y
   ```

2. **部署後端應用**
   ```bash
   # 創建應用程式目錄
   sudo mkdir -p /opt/hb-engineering
   sudo chown $USER:$USER /opt/hb-engineering
   cd /opt/hb-engineering
   
   # 上傳或克隆後端文件
   # 創建虛擬環境
   python3 -m venv venv
   source venv/bin/activate
   
   # 安裝依賴
   pip install -r requirements.txt
   ```

3. **配置系統服務**
   ```bash
   # 創建 systemd 服務
   sudo cp systemd/hb-engineering.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable hb-engineering.service
   sudo systemctl start hb-engineering.service
   ```

4. **驗證部署**
   ```bash
   # 檢查服務狀態
   sudo systemctl status hb-engineering
   
   # 測試API
   curl http://localhost:5000/api/system/health
   ```

#### 前端開發與構建

1. **安裝依賴**
   ```bash
   cd HBEngineeringFrontend
   npm install
   ```

2. **開發模式運行**
   ```bash
   npm run dev
   ```

3. **打包應用程式**
   ```bash
   # Windows
   npm run build:win
   
   # macOS
   npm run build:mac
   
   # Linux
   npm run build:linux
   ```

### 使用指南

#### 系統初次設定
1. **啟動樹莓派後端服務**
   - 檢查服務狀態: `sudo systemctl status hb-engineering`
   - 確認API可用: 訪問 `http://樹莓派IP:5000/api/system/health`

2. **啟動前端應用程式**
   - 運行Electron桌面應用
   - 輸入樹莓派IP地址配置後端連接

3. **管理員首次登入**
   - 用戶名: `admin`
   - 密碼: `admin123` (請立即修改)

4. **設定公司資訊**
   - 進入「設定」→「公司資訊」
   - 更新公司名稱、地址、聯絡方式等

5. **建立用戶帳號**
   - 管理員可在「用戶管理」中新增使用者
   - 設定不同權限等級

#### 業務工作流程
1. **客戶管理**
   - 新增客戶資料 → 維護聯絡資訊 → 建立客戶檔案

2. **產品管理**
   - 建立產品目錄 → 設定價格 → 管理庫存

3. **報價單流程**
   - 選擇客戶 → 添加產品/服務 → 計算價格 → 生成PDF → 發送客戶

4. **發票流程**
   - 報價單轉發票 → 確認內容 → 生成發票PDF → 追蹤付款狀態

5. **送貨單流程**
   - 發票轉送貨單 → 安排送貨 → 記錄送貨狀態 → 收集簽收

#### 系統功能
- **中英文切換**: 在設定中選擇界面語言
- **PDF生成**: 自動生成專業格式的報價單、發票、送貨單
- **數據備份**: 系統自動每日備份數據庫
- **多用戶支援**: 支援多用戶同時使用，權限管理

### 配置檔案

#### 後端環境配置 (`.env`)
```bash
# Flask環境配置
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 數據庫配置
DATABASE_PATH=database/hb_engineering.db

# 服務配置
HOST=0.0.0.0
PORT=5000

# 日誌配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# CORS配置
CORS_ORIGINS=*
```

#### 前端配置 (`src/config/app.config.js`)
```javascript
export const API_CONFIG = {
  BASE_URL: 'http://*************:5000/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3
};

export const APP_CONFIG = {
  DEFAULT_LANGUAGE: 'zh-HK',
  THEME: 'light',
  AUTO_SAVE_INTERVAL: 30000
};
```

### 開發指南

#### 新增功能模塊
1. 在對應目錄建立 `.h` 和 `.cpp` 檔案
2. 在 `CMakeLists.txt` 中新增檔案路徑
3. 實作業務邏輯和界面
4. 新增單元測試

#### 數據庫架構
- 使用 SQLite 作為本地數據庫
- 所有數據操作透過 `DatabaseManager` 類
- 支援資料庫備份與恢復

#### 國際化
- 使用 Qt 的 `tr()` 函數包裝所有文字
- 執行 `lupdate` 更新翻譯檔案
- 執行 `lrelease` 生成 `.qm` 檔案

### 故障排除

#### 常見問題
1. **無法啟動**: 檢查 Qt6 庫是否正確安裝
2. **數據庫錯誤**: 檢查數據庫檔案權限
3. **PDF 生成失敗**: 檢查磁碟空間和檔案權限
4. **中文顯示問題**: 確認翻譯檔案已正確編譯

#### 日誌檔案
- 應用程式日誌: `logs/application.log`
- 錯誤日誌: `logs/error.log`

### API 文檔

詳細的API文檔請參考 [`API_DOCUMENTATION.md`](API_DOCUMENTATION.md)，包含：
- 完整的API端點說明
- 請求/回應格式
- 認證方式
- 錯誤處理
- 前端整合範例

### 樹莓派部署

詳細的樹莓派部署指南請參考 [`RASPBERRY_PI_SETUP.md`](RASPBERRY_PI_SETUP.md)，包含：
- 系統環境準備
- 應用程式安裝配置
- 系統服務設定
- 維護和故障排除
- 前端啟動時自動啟動後端的方法

### 版本更新

#### v2.0.0 (目前版本 - 前後端分離)
- ✅ Flask RESTful API 後端
- ✅ 樹莓派部署支援
- ✅ JWT 認證機制
- ✅ 完整的API文檔
- ✅ 基本報價單、發票、送貨單功能
- ✅ 中英文雙語支援
- ✅ PDF 生成與下載
- ✅ SQLite 數據庫支援
- ✅ 系統服務自動啟動
- ✅ 數據備份機制

#### 計劃功能 (v2.1.0)
- 🔄 發票和送貨單 API 完善
- 🔄 庫存管理模塊
- 🔄 報表統計功能
- 🔄 資料匯入/匯出
- 🔄 電子簽名支援
- 🔄 HTTPS 安全連接
- 🔄 用戶權限細分

### 技術支援

- **開發公司**: 蒼藍工程公司
- **聯絡電話**: +852 XXXXXXXX
- **電子郵件**: <EMAIL>
- **官方網站**: https://hbengineering.com.hk

### 授權資訊

此軟體為蒼藍工程公司專用系統，未經授權不得複製或分發。

---

**注意**: 本系統專為香港地區的電力工程業務設計，包含符合香港商業習慣的單據格式和術語。 

**.\HB_Engineering_Complete.exe**
