import { ref, onMounted, onUnmounted } from 'vue';

// 性能監控組合式函數
export function usePerformance() {
  const performanceData = ref({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    fps: 0,
  });

  let startTime = 0;
  let frameCount = 0;
  let lastTime = 0;
  let animationId: number;

  // 測量頁面加載時間
  const measureLoadTime = () => {
    if (performance.timing) {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      performanceData.value.loadTime = loadTime;
    }
  };

  // 測量渲染時間
  const measureRenderTime = () => {
    startTime = performance.now();
  };

  const endRenderTime = () => {
    if (startTime) {
      performanceData.value.renderTime = performance.now() - startTime;
    }
  };

  // 測量內存使用
  const measureMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      performanceData.value.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
    }
  };

  // 測量 FPS
  const measureFPS = (currentTime: number) => {
    frameCount++;
    if (currentTime - lastTime >= 1000) {
      performanceData.value.fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
      frameCount = 0;
      lastTime = currentTime;
    }
    animationId = requestAnimationFrame(measureFPS);
  };

  // 開始性能監控
  const startMonitoring = () => {
    measureLoadTime();
    measureRenderTime();
    measureMemoryUsage();
    lastTime = performance.now();
    animationId = requestAnimationFrame(measureFPS);
  };

  // 停止性能監控
  const stopMonitoring = () => {
    if (animationId) {
      cancelAnimationFrame(animationId);
    }
  };

  // 獲取性能報告
  const getPerformanceReport = () => {
    return {
      ...performanceData.value,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
  };

  onMounted(() => {
    startMonitoring();
  });

  onUnmounted(() => {
    stopMonitoring();
  });

  return {
    performanceData,
    measureRenderTime,
    endRenderTime,
    measureMemoryUsage,
    getPerformanceReport,
    startMonitoring,
    stopMonitoring,
  };
}

// 虛擬滾動組合式函數
export function useVirtualScroll<T>(
  items: Ref<T[]>,
  itemHeight: number,
  containerHeight: number
) {
  const scrollTop = ref(0);
  const startIndex = computed(() => Math.floor(scrollTop.value / itemHeight));
  const endIndex = computed(() => 
    Math.min(
      startIndex.value + Math.ceil(containerHeight / itemHeight) + 1,
      items.value.length
    )
  );
  const visibleItems = computed(() => 
    items.value.slice(startIndex.value, endIndex.value)
  );
  const totalHeight = computed(() => items.value.length * itemHeight);
  const offsetY = computed(() => startIndex.value * itemHeight);

  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement;
    scrollTop.value = target.scrollTop;
  };

  return {
    scrollTop,
    startIndex,
    endIndex,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
  };
}

// 圖片懶加載組合式函數
export function useLazyLoad() {
  const observer = ref<IntersectionObserver | null>(null);
  const loadedImages = ref(new Set<string>());

  const createObserver = () => {
    if ('IntersectionObserver' in window) {
      observer.value = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              const src = img.dataset.src;
              if (src && !loadedImages.value.has(src)) {
                img.src = src;
                loadedImages.value.add(src);
                observer.value?.unobserve(img);
              }
            }
          });
        },
        {
          rootMargin: '50px',
        }
      );
    }
  };

  const observe = (element: HTMLElement) => {
    if (observer.value) {
      observer.value.observe(element);
    }
  };

  const unobserve = (element: HTMLElement) => {
    if (observer.value) {
      observer.value.unobserve(element);
    }
  };

  const disconnect = () => {
    if (observer.value) {
      observer.value.disconnect();
    }
  };

  onMounted(() => {
    createObserver();
  });

  onUnmounted(() => {
    disconnect();
  });

  return {
    observe,
    unobserve,
    disconnect,
    loadedImages,
  };
}

// 防抖組合式函數
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
) {
  let timeoutId: number;

  const debouncedFn = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = window.setTimeout(() => fn(...args), delay);
  };

  const cancel = () => {
    clearTimeout(timeoutId);
  };

  onUnmounted(() => {
    cancel();
  });

  return {
    debouncedFn: debouncedFn as T,
    cancel,
  };
}

// 節流組合式函數
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
) {
  let lastCall = 0;

  const throttledFn = (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return fn(...args);
    }
  };

  return {
    throttledFn: throttledFn as T,
  };
}

// 緩存組合式函數
export function useCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 5 * 60 * 1000 // 5分鐘
) {
  const cache = ref<Map<string, { data: T; timestamp: number }>>(new Map());

  const get = async (): Promise<T> => {
    const cached = cache.value.get(key);
    const now = Date.now();

    if (cached && now - cached.timestamp < ttl) {
      return cached.data;
    }

    const data = await fetcher();
    cache.value.set(key, { data, timestamp: now });
    return data;
  };

  const clear = () => {
    cache.value.delete(key);
  };

  const clearAll = () => {
    cache.value.clear();
  };

  return {
    get,
    clear,
    clearAll,
  };
}

// Web Worker 組合式函數
export function useWebWorker<T, R>(
  workerScript: string
) {
  const worker = ref<Worker | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const createWorker = () => {
    if ('Worker' in window) {
      worker.value = new Worker(workerScript);
    }
  };

  const postMessage = (data: T): Promise<R> => {
    return new Promise((resolve, reject) => {
      if (!worker.value) {
        reject(new Error('Worker not available'));
        return;
      }

      loading.value = true;
      error.value = null;

      const handleMessage = (event: MessageEvent<R>) => {
        loading.value = false;
        worker.value?.removeEventListener('message', handleMessage);
        worker.value?.removeEventListener('error', handleError);
        resolve(event.data);
      };

      const handleError = (event: ErrorEvent) => {
        loading.value = false;
        error.value = event.message;
        worker.value?.removeEventListener('message', handleMessage);
        worker.value?.removeEventListener('error', handleError);
        reject(new Error(event.message));
      };

      worker.value.addEventListener('message', handleMessage);
      worker.value.addEventListener('error', handleError);
      worker.value.postMessage(data);
    });
  };

  const terminate = () => {
    if (worker.value) {
      worker.value.terminate();
      worker.value = null;
    }
  };

  onMounted(() => {
    createWorker();
  });

  onUnmounted(() => {
    terminate();
  });

  return {
    worker,
    loading,
    error,
    postMessage,
    terminate,
  };
}
