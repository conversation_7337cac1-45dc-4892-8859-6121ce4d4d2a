{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "confirm": "Confirm", "back": "Back", "loading": "Loading...", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info"}, "menu": {"dashboard": "Dashboard", "quotations": "Quotations", "invoices": "Invoices", "deliveryNotes": "Delivery Notes", "customers": "Customers", "products": "Products", "settings": "Settings"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?"}, "quotation": {"title": "Quotation", "create": "Create Quotation", "edit": "Edit Quotation", "list": "Quotation List", "number": "Quotation No.", "customer": "Customer", "date": "Date", "validUntil": "<PERSON>id <PERSON>", "status": "Status", "total": "Total Amount", "discount": "Discount", "finalAmount": "Final Amount", "items": "Items", "addItem": "Add Item", "removeItem": "Remove Item", "product": "Product", "quantity": "Quantity", "unitPrice": "Unit Price", "amount": "Amount", "notes": "Notes"}, "invoice": {"title": "Invoice", "create": "Create Invoice", "edit": "Edit Invoice", "list": "Invoice List", "number": "Invoice No.", "customer": "Customer", "date": "Date", "dueDate": "Due Date", "status": "Status", "total": "Total Amount", "tax": "Tax", "finalAmount": "Final Amount", "items": "Items", "addItem": "Add Item", "removeItem": "Remove Item", "product": "Product", "quantity": "Quantity", "unitPrice": "Unit Price", "amount": "Amount", "notes": "Notes"}, "deliveryNote": {"title": "Delivery Note", "create": "Create Delivery Note", "edit": "Edit Delivery Note", "list": "Delivery Note List", "number": "Delivery Note No.", "customer": "Customer", "date": "Date", "status": "Status", "items": "Items", "addItem": "Add Item", "removeItem": "Remove Item", "product": "Product", "quantity": "Quantity", "notes": "Notes", "signature": "Signature"}, "customer": {"title": "Customer", "create": "Create Customer", "edit": "Edit Customer", "list": "Customer List", "name": "Company Name", "contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "address": "Address", "companyRegNo": "Company Reg. No."}, "product": {"title": "Product", "create": "Create Product", "edit": "Edit Product", "list": "Product List", "name": "Product Name", "description": "Description", "category": "Category", "unit": "Unit", "unitPrice": "Unit Price", "isActive": "Is Active"}, "status": {"draft": "Draft", "sent": "<PERSON><PERSON>", "accepted": "Accepted", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled"}}