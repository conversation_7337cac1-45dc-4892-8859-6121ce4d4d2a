// 數據處理 Web Worker
// 用於處理大量數據計算，避免阻塞主線程

self.onmessage = function(e) {
  const { type, data, options } = e.data;

  try {
    let result;

    switch (type) {
      case 'SORT_DATA':
        result = sortData(data, options);
        break;
      case 'FILTER_DATA':
        result = filterData(data, options);
        break;
      case 'CALCULATE_STATISTICS':
        result = calculateStatistics(data, options);
        break;
      case 'PROCESS_EXCEL_DATA':
        result = processExcelData(data, options);
        break;
      case 'GENERATE_REPORT':
        result = generateReport(data, options);
        break;
      default:
        throw new Error(`Unknown operation type: ${type}`);
    }

    self.postMessage({
      success: true,
      result,
      type
    });
  } catch (error) {
    self.postMessage({
      success: false,
      error: error.message,
      type
    });
  }
};

// 數據排序
function sortData(data, options) {
  const { field, order = 'asc', type = 'string' } = options;
  
  return data.sort((a, b) => {
    let valueA = a[field];
    let valueB = b[field];

    // 處理不同數據類型
    if (type === 'number') {
      valueA = parseFloat(valueA) || 0;
      valueB = parseFloat(valueB) || 0;
    } else if (type === 'date') {
      valueA = new Date(valueA).getTime();
      valueB = new Date(valueB).getTime();
    } else {
      valueA = String(valueA).toLowerCase();
      valueB = String(valueB).toLowerCase();
    }

    if (valueA < valueB) {
      return order === 'asc' ? -1 : 1;
    }
    if (valueA > valueB) {
      return order === 'asc' ? 1 : -1;
    }
    return 0;
  });
}

// 數據過濾
function filterData(data, options) {
  const { filters } = options;
  
  return data.filter(item => {
    return filters.every(filter => {
      const { field, operator, value, type = 'string' } = filter;
      let itemValue = item[field];
      let filterValue = value;

      // 處理不同數據類型
      if (type === 'number') {
        itemValue = parseFloat(itemValue) || 0;
        filterValue = parseFloat(filterValue) || 0;
      } else if (type === 'date') {
        itemValue = new Date(itemValue).getTime();
        filterValue = new Date(filterValue).getTime();
      } else {
        itemValue = String(itemValue).toLowerCase();
        filterValue = String(filterValue).toLowerCase();
      }

      // 應用過濾操作符
      switch (operator) {
        case 'equals':
          return itemValue === filterValue;
        case 'not_equals':
          return itemValue !== filterValue;
        case 'contains':
          return itemValue.includes(filterValue);
        case 'not_contains':
          return !itemValue.includes(filterValue);
        case 'starts_with':
          return itemValue.startsWith(filterValue);
        case 'ends_with':
          return itemValue.endsWith(filterValue);
        case 'greater_than':
          return itemValue > filterValue;
        case 'greater_than_or_equal':
          return itemValue >= filterValue;
        case 'less_than':
          return itemValue < filterValue;
        case 'less_than_or_equal':
          return itemValue <= filterValue;
        case 'between':
          const [min, max] = filterValue;
          return itemValue >= min && itemValue <= max;
        case 'in':
          return Array.isArray(filterValue) && filterValue.includes(itemValue);
        case 'not_in':
          return Array.isArray(filterValue) && !filterValue.includes(itemValue);
        default:
          return true;
      }
    });
  });
}

// 計算統計數據
function calculateStatistics(data, options) {
  const { fields } = options;
  const stats = {};

  fields.forEach(field => {
    const values = data.map(item => parseFloat(item[field]) || 0);
    const validValues = values.filter(value => !isNaN(value));

    if (validValues.length === 0) {
      stats[field] = {
        count: 0,
        sum: 0,
        average: 0,
        min: 0,
        max: 0,
        median: 0,
        standardDeviation: 0
      };
      return;
    }

    const sum = validValues.reduce((acc, value) => acc + value, 0);
    const average = sum / validValues.length;
    const min = Math.min(...validValues);
    const max = Math.max(...validValues);
    
    // 計算中位數
    const sortedValues = [...validValues].sort((a, b) => a - b);
    const median = sortedValues.length % 2 === 0
      ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
      : sortedValues[Math.floor(sortedValues.length / 2)];

    // 計算標準差
    const variance = validValues.reduce((acc, value) => acc + Math.pow(value - average, 2), 0) / validValues.length;
    const standardDeviation = Math.sqrt(variance);

    stats[field] = {
      count: validValues.length,
      sum: Math.round(sum * 100) / 100,
      average: Math.round(average * 100) / 100,
      min,
      max,
      median: Math.round(median * 100) / 100,
      standardDeviation: Math.round(standardDeviation * 100) / 100
    };
  });

  return stats;
}

// 處理 Excel 數據
function processExcelData(data, options) {
  const { mapping, validation = true } = options;
  const processed = [];
  const errors = [];

  data.forEach((row, index) => {
    const processedRow = {};
    let hasError = false;

    // 映射字段
    Object.keys(mapping).forEach(targetField => {
      const sourceField = mapping[targetField];
      let value = row[sourceField];

      // 數據轉換
      if (targetField.includes('_date') && value) {
        value = new Date(value).toISOString().split('T')[0];
      } else if (targetField.includes('_amount') && value) {
        value = parseFloat(value) || 0;
      } else if (targetField.includes('_quantity') && value) {
        value = parseInt(value) || 0;
      }

      processedRow[targetField] = value;
    });

    // 數據驗證
    if (validation) {
      const validationErrors = validateRow(processedRow, index + 1);
      if (validationErrors.length > 0) {
        hasError = true;
        errors.push(...validationErrors);
      }
    }

    if (!hasError) {
      processed.push(processedRow);
    }
  });

  return {
    processed,
    errors,
    totalRows: data.length,
    processedRows: processed.length,
    errorRows: errors.length
  };
}

// 行數據驗證
function validateRow(row, rowIndex) {
  const errors = [];

  // 必填字段檢查
  const requiredFields = ['customer_name', 'product_name', 'quantity', 'unit_price'];
  requiredFields.forEach(field => {
    if (!row[field] || row[field] === '') {
      errors.push({
        row: rowIndex,
        field,
        message: `第 ${rowIndex} 行的 ${field} 字段不能為空`
      });
    }
  });

  // 數值字段檢查
  const numericFields = ['quantity', 'unit_price', 'discount_rate', 'tax_rate'];
  numericFields.forEach(field => {
    if (row[field] !== undefined && row[field] !== '' && isNaN(parseFloat(row[field]))) {
      errors.push({
        row: rowIndex,
        field,
        message: `第 ${rowIndex} 行的 ${field} 字段必須是數字`
      });
    }
  });

  // 日期字段檢查
  const dateFields = ['valid_until', 'created_date'];
  dateFields.forEach(field => {
    if (row[field] && isNaN(new Date(row[field]).getTime())) {
      errors.push({
        row: rowIndex,
        field,
        message: `第 ${rowIndex} 行的 ${field} 字段不是有效日期`
      });
    }
  });

  return errors;
}

// 生成報表
function generateReport(data, options) {
  const { type, groupBy, aggregations } = options;

  switch (type) {
    case 'summary':
      return generateSummaryReport(data, groupBy, aggregations);
    case 'trend':
      return generateTrendReport(data, options);
    case 'comparison':
      return generateComparisonReport(data, options);
    default:
      throw new Error(`Unknown report type: ${type}`);
  }
}

// 生成摘要報表
function generateSummaryReport(data, groupBy, aggregations) {
  const groups = {};

  // 分組數據
  data.forEach(item => {
    const groupKey = groupBy.map(field => item[field]).join('|');
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(item);
  });

  // 計算聚合值
  const report = Object.keys(groups).map(groupKey => {
    const groupData = groups[groupKey];
    const result = {};

    // 設置分組字段
    groupBy.forEach((field, index) => {
      result[field] = groupKey.split('|')[index];
    });

    // 計算聚合值
    aggregations.forEach(agg => {
      const { field, operation } = agg;
      const values = groupData.map(item => parseFloat(item[field]) || 0);

      switch (operation) {
        case 'sum':
          result[`${field}_sum`] = values.reduce((acc, val) => acc + val, 0);
          break;
        case 'average':
          result[`${field}_avg`] = values.reduce((acc, val) => acc + val, 0) / values.length;
          break;
        case 'count':
          result[`${field}_count`] = values.length;
          break;
        case 'min':
          result[`${field}_min`] = Math.min(...values);
          break;
        case 'max':
          result[`${field}_max`] = Math.max(...values);
          break;
      }
    });

    return result;
  });

  return report;
}

// 生成趨勢報表
function generateTrendReport(data, options) {
  const { dateField, valueField, period = 'month' } = options;
  const trends = {};

  data.forEach(item => {
    const date = new Date(item[dateField]);
    let periodKey;

    switch (period) {
      case 'day':
        periodKey = date.toISOString().split('T')[0];
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        periodKey = weekStart.toISOString().split('T')[0];
        break;
      case 'month':
        periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      case 'year':
        periodKey = date.getFullYear().toString();
        break;
    }

    if (!trends[periodKey]) {
      trends[periodKey] = [];
    }
    trends[periodKey].push(parseFloat(item[valueField]) || 0);
  });

  return Object.keys(trends).sort().map(period => ({
    period,
    value: trends[period].reduce((acc, val) => acc + val, 0),
    count: trends[period].length,
    average: trends[period].reduce((acc, val) => acc + val, 0) / trends[period].length
  }));
}

// 生成對比報表
function generateComparisonReport(data, options) {
  const { compareField, valueField, periods } = options;
  const comparison = {};

  periods.forEach(period => {
    const periodData = data.filter(item => {
      const itemDate = new Date(item.created_date);
      return itemDate >= new Date(period.start) && itemDate <= new Date(period.end);
    });

    comparison[period.name] = {};

    // 按對比字段分組
    const groups = {};
    periodData.forEach(item => {
      const groupKey = item[compareField];
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(parseFloat(item[valueField]) || 0);
    });

    Object.keys(groups).forEach(groupKey => {
      comparison[period.name][groupKey] = {
        total: groups[groupKey].reduce((acc, val) => acc + val, 0),
        count: groups[groupKey].length,
        average: groups[groupKey].reduce((acc, val) => acc + val, 0) / groups[groupKey].length
      };
    });
  });

  return comparison;
}
