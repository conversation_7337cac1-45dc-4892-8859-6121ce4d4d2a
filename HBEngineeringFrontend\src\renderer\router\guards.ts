import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';
import { useUserStore } from '@stores/user';
import { useSecurityLogger } from '@/composables/useSecurity';
import { isSafeRedirectUrl } from '@/utils/security';
import { ElMessage } from 'element-plus';

// 認證守衛
export function authGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  const userStore = useUserStore();
  const { logPermissionDenied, logSuspiciousActivity } = useSecurityLogger();

  // 檢查是否需要認證
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false);
  
  if (requiresAuth && !userStore.isLoggedIn) {
    // 記錄未授權訪問嘗試
    logPermissionDenied('access', to.path);
    
    // 保存原始目標路由
    const redirectPath = to.fullPath !== '/login' ? to.fullPath : undefined;
    
    next({
      path: '/login',
      query: redirectPath ? { redirect: redirectPath } : undefined,
    });
    return;
  }

  // 檢查會話是否有效
  if (requiresAuth && userStore.isLoggedIn) {
    const isSessionValid = userStore.checkSession();
    if (!isSessionValid) {
      logSuspiciousActivity('Invalid session detected');
      userStore.logout();
      next('/login');
      return;
    }
  }

  next();
}

// 權限守衛
export function permissionGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  const userStore = useUserStore();
  const { logPermissionDenied } = useSecurityLogger();

  // 檢查路由權限
  const requiredPermissions = to.meta.permissions as string[] | undefined;
  const requireAllPermissions = to.meta.requireAllPermissions as boolean | undefined;

  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasPermission = requireAllPermissions
      ? userStore.hasAllPermissions(requiredPermissions)
      : userStore.hasAnyPermission(requiredPermissions);

    if (!hasPermission) {
      logPermissionDenied(requiredPermissions.join(', '), to.path);
      ElMessage.error('您沒有訪問此頁面的權限');
      
      // 重定向到上一頁或首頁
      if (from.path !== '/') {
        next(false); // 阻止導航
      } else {
        next('/'); // 重定向到首頁
      }
      return;
    }
  }

  // 檢查角色權限
  const requiredRoles = to.meta.roles as string[] | undefined;
  if (requiredRoles && requiredRoles.length > 0) {
    const userRole = userStore.userRole;
    if (!requiredRoles.includes(userRole)) {
      logPermissionDenied(`role:${userRole}`, to.path);
      ElMessage.error('您的角色無法訪問此頁面');
      next(false);
      return;
    }
  }

  next();
}

// 重定向安全守衛
export function redirectGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  const { logSuspiciousActivity } = useSecurityLogger();

  // 檢查重定向參數
  const redirectUrl = to.query.redirect as string;
  if (redirectUrl) {
    // 驗證重定向 URL 是否安全
    if (!isSafeRedirectUrl(redirectUrl)) {
      logSuspiciousActivity(`Unsafe redirect attempt: ${redirectUrl}`);
      ElMessage.warning('檢測到不安全的重定向，已阻止');
      next('/');
      return;
    }
  }

  // 檢查是否有可疑的查詢參數
  const suspiciousParams = ['<script', 'javascript:', 'data:', 'vbscript:'];
  const queryString = JSON.stringify(to.query).toLowerCase();
  
  for (const param of suspiciousParams) {
    if (queryString.includes(param)) {
      logSuspiciousActivity(`Suspicious query parameter detected: ${queryString}`);
      ElMessage.warning('檢測到可疑參數，已清理');
      
      // 清理查詢參數並重定向
      next({ path: to.path, query: {} });
      return;
    }
  }

  next();
}

// 速率限制守衛
export function rateLimitGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  const { logSuspiciousActivity } = useSecurityLogger();
  
  // 簡單的客戶端速率限制
  const now = Date.now();
  const navigationHistory = JSON.parse(
    sessionStorage.getItem('navigationHistory') || '[]'
  ) as number[];
  
  // 清理 1 分鐘前的記錄
  const recentNavigations = navigationHistory.filter(time => now - time < 60000);
  
  // 檢查是否超過限制（每分鐘最多 30 次導航）
  if (recentNavigations.length >= 30) {
    logSuspiciousActivity('Excessive navigation attempts detected');
    ElMessage.warning('導航過於頻繁，請稍後再試');
    next(false);
    return;
  }
  
  // 記錄當前導航
  recentNavigations.push(now);
  sessionStorage.setItem('navigationHistory', JSON.stringify(recentNavigations));
  
  next();
}

// 開發環境守衛
export function developmentGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  // 檢查是否為開發專用路由
  const isDevelopmentRoute = to.meta.development as boolean;
  const isProduction = import.meta.env.PROD;
  
  if (isDevelopmentRoute && isProduction) {
    ElMessage.error('此功能僅在開發環境中可用');
    next('/');
    return;
  }
  
  next();
}

// 維護模式守衛
export function maintenanceGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  const isMaintenanceMode = import.meta.env.VITE_MAINTENANCE_MODE === 'true';
  const isMaintenancePage = to.path === '/maintenance';
  const userStore = useUserStore();
  
  if (isMaintenanceMode && !isMaintenancePage) {
    // 允許超級管理員繞過維護模式
    if (userStore.isSuperAdmin) {
      next();
      return;
    }
    
    next('/maintenance');
    return;
  }
  
  if (!isMaintenanceMode && isMaintenancePage) {
    next('/');
    return;
  }
  
  next();
}

// 安全標頭守衛
export function securityHeaderGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  // 設置安全相關的 meta 標籤
  const setSecurityHeaders = () => {
    // CSP (Content Security Policy)
    let cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (!cspMeta) {
      cspMeta = document.createElement('meta');
      cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
      document.head.appendChild(cspMeta);
    }
    
    const cspContent = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self' https:",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ');
    
    cspMeta.setAttribute('content', cspContent);
    
    // X-Frame-Options
    let frameMeta = document.querySelector('meta[http-equiv="X-Frame-Options"]');
    if (!frameMeta) {
      frameMeta = document.createElement('meta');
      frameMeta.setAttribute('http-equiv', 'X-Frame-Options');
      frameMeta.setAttribute('content', 'DENY');
      document.head.appendChild(frameMeta);
    }
    
    // X-Content-Type-Options
    let contentTypeMeta = document.querySelector('meta[http-equiv="X-Content-Type-Options"]');
    if (!contentTypeMeta) {
      contentTypeMeta = document.createElement('meta');
      contentTypeMeta.setAttribute('http-equiv', 'X-Content-Type-Options');
      contentTypeMeta.setAttribute('content', 'nosniff');
      document.head.appendChild(contentTypeMeta);
    }
    
    // Referrer-Policy
    let referrerMeta = document.querySelector('meta[name="referrer"]');
    if (!referrerMeta) {
      referrerMeta = document.createElement('meta');
      referrerMeta.setAttribute('name', 'referrer');
      referrerMeta.setAttribute('content', 'strict-origin-when-cross-origin');
      document.head.appendChild(referrerMeta);
    }
  };
  
  // 在下一個 tick 中設置安全標頭
  setTimeout(setSecurityHeaders, 0);
  
  next();
}

// 組合所有守衛
export function setupRouterGuards(router: any) {
  // 全局前置守衛
  router.beforeEach(maintenanceGuard);
  router.beforeEach(rateLimitGuard);
  router.beforeEach(redirectGuard);
  router.beforeEach(authGuard);
  router.beforeEach(permissionGuard);
  router.beforeEach(developmentGuard);
  router.beforeEach(securityHeaderGuard);
  
  // 全局後置守衛
  router.afterEach((to: RouteLocationNormalized) => {
    // 更新頁面標題
    const title = to.meta.title as string;
    if (title) {
      document.title = `${title} - 蒼藍工程公司管理系統`;
    }
    
    // 記錄頁面訪問
    const { logDataAccess } = useSecurityLogger();
    logDataAccess(to.path, 'page_view');
  });
}
