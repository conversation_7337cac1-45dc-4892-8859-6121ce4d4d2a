const { app } = require('electron');
const path = require('path');

// 測試環境變量
process.env.NODE_ENV = 'development';
process.env.API_URL = 'http://localhost:5000/api';

// 測試配置
app.on('ready', () => {
  console.log('應用程序已啟動');
  console.log('環境變量:', {
    NODE_ENV: process.env.NODE_ENV,
    API_URL: process.env.API_URL
  });
  
  // 測試路徑
  console.log('應用程序路徑:', app.getAppPath());
  console.log('用戶數據路徑:', app.getPath('userData'));
  
  // 測試窗口創建
  const { BrowserWindow } = require('electron');
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  // 加載測試頁面
  win.loadFile(path.join(__dirname, 'src/renderer/index.html'));
  
  // 打開開發者工具
  win.webContents.openDevTools();
  
  // 監聽窗口關閉
  win.on('closed', () => {
    console.log('測試窗口已關閉');
    app.quit();
  });
}); 