# 生產環境配置

# 應用基本配置
VITE_APP_TITLE=蒼藍工程公司管理系統
VITE_APP_VERSION=2.0.0
VITE_APP_DESCRIPTION=現代化企業管理平台

# API 配置
VITE_API_BASE_URL=/api
VITE_API_TIMEOUT=30000

# 後端服務配置
VITE_BACKEND_HOST=your-production-host
VITE_BACKEND_PORT=5000

# 安全配置 (請在生產環境中更改這些值)
VITE_ENCRYPTION_KEY=your-production-encryption-key
VITE_JWT_SECRET=your-production-jwt-secret

# 功能開關
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true

# 系統配置
VITE_SESSION_TIMEOUT=1800000
VITE_AUTO_LOGOUT_WARNING=300000
VITE_MAINTENANCE_MODE=false

# 構建配置
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
VITE_BUILD_ANALYZE=false

# 日誌配置
VITE_LOG_LEVEL=warn
VITE_LOG_TO_CONSOLE=false
VITE_LOG_TO_FILE=true

# 緩存配置
VITE_CACHE_ENABLED=true
VITE_CACHE_TTL=300000
VITE_CACHE_MAX_SIZE=100

# 性能配置
VITE_ENABLE_PWA=true
VITE_ENABLE_COMPRESSION=true
VITE_VIRTUAL_SCROLL_THRESHOLD=100

# 安全配置
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_HSTS=true
VITE_RATE_LIMIT_REQUESTS=100
VITE_RATE_LIMIT_WINDOW=60000
