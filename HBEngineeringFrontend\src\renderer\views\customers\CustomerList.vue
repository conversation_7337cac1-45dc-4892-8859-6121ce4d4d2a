<template>
  <div class="customer-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>客戶管理</span>
          <el-button type="primary" @click="showDialog = true">
            <el-icon><Plus /></el-icon>
            新增客戶
          </el-button>
        </div>
      </template>

      <!-- 搜索篩選 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input
              v-model="searchForm.search"
              placeholder="搜索客戶名稱、聯絡人或電話"
              clearable
              @keyup.enter="handleSearch">
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="12">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 數據表格 -->
      <el-table :data="customers" v-loading="loading" style="width: 100%">
        <el-table-column prop="name" label="公司名稱" width="200" />
        <el-table-column prop="contact_person" label="聯絡人" width="120" />
        <el-table-column prop="phone" label="電話" width="140" />
        <el-table-column prop="email" label="電郵" width="180" />
        <el-table-column prop="address" label="地址" show-overflow-tooltip />
        <el-table-column prop="company_reg_no" label="公司註冊號" width="140" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              編輯
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/編輯對話框 -->
    <el-dialog
      v-model="showDialog"
      :title="isEdit ? '編輯客戶' : '新增客戶'"
      width="600px"
      @close="handleDialogClose">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px">
        <el-form-item label="公司名稱" prop="name">
          <el-input v-model="form.name" placeholder="請輸入公司名稱" />
        </el-form-item>
        <el-form-item label="聯絡人" prop="contact_person">
          <el-input v-model="form.contact_person" placeholder="請輸入聯絡人姓名" />
        </el-form-item>
        <el-form-item label="電話" prop="phone">
          <el-input v-model="form.phone" placeholder="請輸入電話號碼" />
        </el-form-item>
        <el-form-item label="電郵" prop="email">
          <el-input v-model="form.email" placeholder="請輸入電郵地址" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input
            v-model="form.address"
            type="textarea"
            :rows="3"
            placeholder="請輸入地址" />
        </el-form-item>
        <el-form-item label="公司註冊號" prop="company_reg_no">
          <el-input v-model="form.company_reg_no" placeholder="請輸入公司註冊號" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '創建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search } from '@element-plus/icons-vue';

// 數據狀態
const customers = ref([]);
const loading = ref(false);
const submitting = ref(false);

// 分頁狀態
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索表單
const searchForm = reactive({
  search: ''
});

// 對話框狀態
const showDialog = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 表單數據
const form = reactive({
  id: null,
  name: '',
  contact_person: '',
  phone: '',
  email: '',
  address: '',
  company_reg_no: ''
});

// 表單驗證規則
const rules = {
  name: [
    { required: true, message: '請輸入公司名稱', trigger: 'blur' }
  ],
  contact_person: [
    { required: true, message: '請輸入聯絡人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '請輸入電話號碼', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '請輸入正確的電郵地址', trigger: 'blur' }
  ]
};

// 獲取客戶列表
const fetchCustomers = async () => {
  loading.value = true;
  try {
    // 模擬API調用
    const mockData = {
      customers: [
        {
          id: 1,
          name: 'ABC電子有限公司',
          contact_person: '張先生',
          phone: '+852 12345678',
          email: '<EMAIL>',
          address: '香港九龍觀塘工業大廈1座10樓',
          company_reg_no: '12345678'
        },
        {
          id: 2,
          name: 'XYZ工程公司',
          contact_person: '李小姐',
          phone: '+852 87654321',
          email: '<EMAIL>',
          address: '香港新界沙田工業邨18號',
          company_reg_no: '87654321'
        },
        {
          id: 3,
          name: '香港電器有限公司',
          contact_person: '王生',
          phone: '+852 11223344',
          email: '<EMAIL>',
          address: '香港島銅鑼灣謝斐道500號',
          company_reg_no: '11223344'
        }
      ],
      total: 3,
      page: 1,
      per_page: 10
    };

    customers.value = mockData.customers;
    total.value = mockData.total;
    
  } catch (error) {
    ElMessage.error('獲取客戶列表失敗');
  } finally {
    loading.value = false;
  }
};

// 搜索處理
const handleSearch = () => {
  currentPage.value = 1;
  fetchCustomers();
};

// 重置搜索
const handleReset = () => {
  searchForm.search = '';
  currentPage.value = 1;
  fetchCustomers();
};

// 分頁處理
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchCustomers();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchCustomers();
};

// 編輯客戶
const handleEdit = (row) => {
  isEdit.value = true;
  Object.assign(form, row);
  showDialog.value = true;
};

// 刪除客戶
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除客戶 ${row.name} 嗎？此操作不可恢復。`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 模擬刪除
    ElMessage.success('刪除成功');
    fetchCustomers();
  } catch {
    // 用戶取消
  }
};

// 對話框關閉處理
const handleDialogClose = () => {
  isEdit.value = false;
  Object.assign(form, {
    id: null,
    name: '',
    contact_person: '',
    phone: '',
    email: '',
    address: '',
    company_reg_no: ''
  });
  formRef.value?.resetFields();
};

// 提交表單
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    submitting.value = true;
    
    // 模擬API調用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success(isEdit.value ? '更新成功' : '創建成功');
    showDialog.value = false;
    fetchCustomers();
    
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message);
    }
  } finally {
    submitting.value = false;
  }
};

onMounted(() => {
  fetchCustomers();
});
</script>

<style scoped>
.customer-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 