import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActive<PERSON>inia, createPinia } from 'pinia';
import { useUserStore } from '@stores/user';
import { createMockUser, createMockApiResponse } from '@/test/setup';
import * as authAPI from '@api/index';

// Mock API
vi.mock('@api/index', () => ({
  authAPI: {
    login: vi.fn(),
    logout: vi.fn(),
    getUserInfo: vi.fn(),
    updateProfile: vi.fn(),
    changePassword: vi.fn(),
    checkSession: vi.fn(),
  },
}));

describe('User Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  describe('初始狀態', () => {
    it('應該有正確的初始狀態', () => {
      const userStore = useUserStore();
      
      expect(userStore.token).toBe('');
      expect(userStore.userInfo).toBeNull();
      expect(userStore.isLoggedIn).toBe(false);
      expect(userStore.isLoading).toBe(false);
    });
  });

  describe('登入功能', () => {
    it('成功登入應該更新用戶狀態', async () => {
      const userStore = useUserStore();
      const mockUser = createMockUser();
      const mockResponse = createMockApiResponse({
        access_token: 'test-token',
        refresh_token: 'test-refresh-token',
        user: mockUser,
        permissions: [],
      });

      vi.mocked(authAPI.authAPI.login).mockResolvedValue(mockResponse);

      await userStore.login({
        username: 'testuser',
        password: 'password123',
      });

      expect(userStore.token).toBe('test-token');
      expect(userStore.userInfo).toEqual(mockUser);
      expect(userStore.isLoggedIn).toBe(true);
    });

    it('登入失敗應該拋出錯誤', async () => {
      const userStore = useUserStore();
      const error = new Error('登入失敗');

      vi.mocked(authAPI.authAPI.login).mockRejectedValue(error);

      await expect(userStore.login({
        username: 'testuser',
        password: 'wrongpassword',
      })).rejects.toThrow('登入失敗');

      expect(userStore.token).toBe('');
      expect(userStore.userInfo).toBeNull();
      expect(userStore.isLoggedIn).toBe(false);
    });
  });

  describe('登出功能', () => {
    it('應該清除用戶狀態', async () => {
      const userStore = useUserStore();
      
      // 先設置一些狀態
      userStore.token = 'test-token';
      userStore.userInfo = createMockUser();

      vi.mocked(authAPI.authAPI.logout).mockResolvedValue(createMockApiResponse(null));

      await userStore.logout();

      expect(userStore.token).toBe('');
      expect(userStore.userInfo).toBeNull();
      expect(userStore.isLoggedIn).toBe(false);
    });
  });

  describe('權限檢查', () => {
    beforeEach(() => {
      const userStore = useUserStore();
      userStore.userInfo = createMockUser();
      userStore.permissions = [
        { id: 1, code: 'user.view', name: '查看用戶', module: 'user', enabled: true },
        { id: 2, code: 'user.create', name: '創建用戶', module: 'user', enabled: true },
        { id: 3, code: 'admin.settings', name: '管理設置', module: 'admin', enabled: false },
      ];
    });

    it('hasPermission 應該正確檢查權限', () => {
      const userStore = useUserStore();

      expect(userStore.hasPermission('user.view')).toBe(true);
      expect(userStore.hasPermission('user.create')).toBe(true);
      expect(userStore.hasPermission('admin.settings')).toBe(false);
      expect(userStore.hasPermission('nonexistent')).toBe(false);
    });

    it('hasAnyPermission 應該正確檢查多個權限', () => {
      const userStore = useUserStore();

      expect(userStore.hasAnyPermission(['user.view', 'user.create'])).toBe(true);
      expect(userStore.hasAnyPermission(['user.view', 'nonexistent'])).toBe(true);
      expect(userStore.hasAnyPermission(['admin.settings', 'nonexistent'])).toBe(false);
    });

    it('hasAllPermissions 應該正確檢查所有權限', () => {
      const userStore = useUserStore();

      expect(userStore.hasAllPermissions(['user.view', 'user.create'])).toBe(true);
      expect(userStore.hasAllPermissions(['user.view', 'admin.settings'])).toBe(false);
      expect(userStore.hasAllPermissions(['nonexistent'])).toBe(false);
    });

    it('超級管理員應該擁有所有權限', () => {
      const userStore = useUserStore();
      userStore.userInfo = { ...createMockUser(), role: 'super_admin' };

      expect(userStore.hasPermission('any.permission')).toBe(true);
      expect(userStore.hasAnyPermission(['any.permission'])).toBe(true);
      expect(userStore.hasAllPermissions(['any.permission', 'another.permission'])).toBe(true);
    });
  });

  describe('用戶信息更新', () => {
    it('應該成功更新用戶信息', async () => {
      const userStore = useUserStore();
      const originalUser = createMockUser();
      userStore.userInfo = originalUser;

      const updatedData = { name: '新名稱', email: '<EMAIL>' };
      const mockResponse = createMockApiResponse({ ...originalUser, ...updatedData });

      vi.mocked(authAPI.authAPI.updateProfile).mockResolvedValue(mockResponse);

      await userStore.updateUserInfo(updatedData);

      expect(userStore.userInfo?.name).toBe('新名稱');
      expect(userStore.userInfo?.email).toBe('<EMAIL>');
    });
  });

  describe('密碼修改', () => {
    it('應該成功修改密碼', async () => {
      const userStore = useUserStore();
      const mockResponse = createMockApiResponse(null);

      vi.mocked(authAPI.authAPI.changePassword).mockResolvedValue(mockResponse);

      await expect(userStore.changePassword('oldpass', 'newpass')).resolves.not.toThrow();
    });

    it('密碼修改失敗應該拋出錯誤', async () => {
      const userStore = useUserStore();
      const error = new Error('舊密碼不正確');

      vi.mocked(authAPI.authAPI.changePassword).mockRejectedValue(error);

      await expect(userStore.changePassword('wrongpass', 'newpass')).rejects.toThrow('舊密碼不正確');
    });
  });

  describe('會話檢查', () => {
    it('有效會話應該返回 true', async () => {
      const userStore = useUserStore();
      userStore.token = 'valid-token';
      
      const mockResponse = createMockApiResponse({ valid: true });
      vi.mocked(authAPI.authAPI.checkSession).mockResolvedValue(mockResponse);

      const result = await userStore.checkSession();
      expect(result).toBe(true);
    });

    it('無效會話應該返回 false', async () => {
      const userStore = useUserStore();
      userStore.token = 'invalid-token';
      
      const mockResponse = createMockApiResponse({ valid: false });
      vi.mocked(authAPI.authAPI.checkSession).mockResolvedValue(mockResponse);

      const result = await userStore.checkSession();
      expect(result).toBe(false);
    });

    it('沒有 token 應該返回 false', async () => {
      const userStore = useUserStore();
      userStore.token = '';

      const result = await userStore.checkSession();
      expect(result).toBe(false);
    });
  });

  describe('計算屬性', () => {
    it('userName 應該返回正確的用戶名', () => {
      const userStore = useUserStore();
      
      expect(userStore.userName).toBe('');
      
      userStore.userInfo = createMockUser();
      expect(userStore.userName).toBe('測試用戶');
    });

    it('userRole 應該返回正確的角色', () => {
      const userStore = useUserStore();
      
      expect(userStore.userRole).toBe('');
      
      userStore.userInfo = createMockUser();
      expect(userStore.userRole).toBe('admin');
    });

    it('isAdmin 應該正確判斷管理員', () => {
      const userStore = useUserStore();
      
      expect(userStore.isAdmin).toBe(false);
      
      userStore.userInfo = { ...createMockUser(), role: 'admin' };
      expect(userStore.isAdmin).toBe(true);
      
      userStore.userInfo = { ...createMockUser(), role: 'employee' };
      expect(userStore.isAdmin).toBe(false);
    });

    it('isSuperAdmin 應該正確判斷超級管理員', () => {
      const userStore = useUserStore();
      
      expect(userStore.isSuperAdmin).toBe(false);
      
      userStore.userInfo = { ...createMockUser(), role: 'super_admin' };
      expect(userStore.isSuperAdmin).toBe(true);
      
      userStore.userInfo = { ...createMockUser(), role: 'admin' };
      expect(userStore.isSuperAdmin).toBe(false);
    });
  });
});
