import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ref } from 'vue';
import { useTable } from '@/composables/useTable';
import { createMockPaginatedResponse, delay } from '@/test/setup';

describe('useTable', () => {
  const mockData = [
    { id: 1, name: '項目1', status: 'active' },
    { id: 2, name: '項目2', status: 'inactive' },
    { id: 3, name: '項目3', status: 'active' },
  ];

  const mockFetchApi = vi.fn();
  const mockDeleteApi = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetchApi.mockResolvedValue(createMockPaginatedResponse(mockData));
    mockDeleteApi.mockResolvedValue(undefined);
  });

  describe('初始化', () => {
    it('應該有正確的初始狀態', () => {
      const { data, loading, pagination, filters } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      expect(data.value).toEqual([]);
      expect(loading.value).toBe(false);
      expect(pagination.value.page).toBe(1);
      expect(pagination.value.size).toBe(20);
      expect(filters.value).toEqual({});
    });

    it('immediate 為 true 時應該自動獲取數據', async () => {
      useTable({
        fetchApi: mockFetchApi,
        immediate: true,
      });

      await delay(0); // 等待異步操作完成
      expect(mockFetchApi).toHaveBeenCalledTimes(1);
    });

    it('應該使用自定義的初始配置', () => {
      const { pagination, filters } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
        pagination: { page: 2, size: 50 },
        defaultFilters: { status: 'active' },
      });

      expect(pagination.value.page).toBe(2);
      expect(pagination.value.size).toBe(50);
      expect(filters.value).toEqual({ status: 'active' });
    });
  });

  describe('數據獲取', () => {
    it('fetchData 應該正確獲取數據', async () => {
      const { data, loading, pagination, fetchData } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      expect(loading.value).toBe(false);

      const promise = fetchData();
      expect(loading.value).toBe(true);

      await promise;

      expect(loading.value).toBe(false);
      expect(data.value).toEqual(mockData);
      expect(pagination.value.total).toBe(mockData.length);
      expect(mockFetchApi).toHaveBeenCalledWith({
        page: 1,
        size: 20,
      });
    });

    it('fetchData 失敗時應該處理錯誤', async () => {
      const error = new Error('網絡錯誤');
      mockFetchApi.mockRejectedValue(error);

      const { data, loading, error: tableError, fetchData } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      await fetchData();

      expect(loading.value).toBe(false);
      expect(data.value).toEqual([]);
      expect(tableError.value).toBe('網絡錯誤');
    });

    it('refresh 應該重新獲取數據', async () => {
      const { refresh } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      await refresh();
      expect(mockFetchApi).toHaveBeenCalledTimes(1);

      await refresh();
      expect(mockFetchApi).toHaveBeenCalledTimes(2);
    });
  });

  describe('分頁功能', () => {
    it('handlePageChange 應該更新頁碼並獲取數據', async () => {
      const { pagination, handlePageChange } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      await handlePageChange(3);

      expect(pagination.value.page).toBe(3);
      expect(mockFetchApi).toHaveBeenCalledWith({
        page: 3,
        size: 20,
      });
    });

    it('handleSizeChange 應該更新頁面大小並重置頁碼', async () => {
      const { pagination, handleSizeChange } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      // 先設置頁碼為 3
      pagination.value.page = 3;

      await handleSizeChange(50);

      expect(pagination.value.page).toBe(1);
      expect(pagination.value.size).toBe(50);
      expect(mockFetchApi).toHaveBeenCalledWith({
        page: 1,
        size: 50,
      });
    });
  });

  describe('排序功能', () => {
    it('handleSortChange 應該更新排序並獲取數據', async () => {
      const { sorting, handleSortChange } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      await handleSortChange({ prop: 'name', order: 'ascending' });

      expect(sorting.value).toEqual({ prop: 'name', order: 'ascending' });
      expect(mockFetchApi).toHaveBeenCalledWith({
        page: 1,
        size: 20,
        sort_by: 'name',
        sort_order: 'asc',
      });
    });
  });

  describe('篩選功能', () => {
    it('setFilters 應該更新篩選條件並重置頁碼', async () => {
      const { filters, pagination, setFilters } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      // 先設置頁碼為 3
      pagination.value.page = 3;

      setFilters({ status: 'active' });

      expect(filters.value).toEqual({ status: 'active' });
      expect(pagination.value.page).toBe(1);
    });

    it('resetFilters 應該重置篩選條件', () => {
      const { filters, resetFilters } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
        defaultFilters: { status: 'active' },
      });

      // 修改篩選條件
      filters.value = { status: 'inactive', name: 'test' };

      resetFilters();

      expect(filters.value).toEqual({ status: 'active' });
    });
  });

  describe('選擇功能', () => {
    it('handleSelectionChange 應該更新選中項', () => {
      const { selectedRows, selectedRowKeys, handleSelectionChange } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      const selectedItems = [mockData[0], mockData[2]];
      handleSelectionChange(selectedItems);

      expect(selectedRows.value).toEqual(selectedItems);
      expect(selectedRowKeys.value).toEqual([1, 3]);
    });

    it('clearSelection 應該清空選中項', () => {
      const { selectedRows, selectedRowKeys, handleSelectionChange, clearSelection } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      // 先選中一些項目
      handleSelectionChange([mockData[0]]);
      expect(selectedRows.value).toHaveLength(1);

      clearSelection();

      expect(selectedRows.value).toEqual([]);
      expect(selectedRowKeys.value).toEqual([]);
    });
  });

  describe('刪除功能', () => {
    it('handleDelete 應該刪除單個項目', async () => {
      // Mock ElMessageBox.confirm
      const mockConfirm = vi.fn().mockResolvedValue('confirm');
      vi.stubGlobal('ElMessageBox', { confirm: mockConfirm });

      const { handleDelete } = useTable({
        fetchApi: mockFetchApi,
        deleteApi: mockDeleteApi,
        immediate: false,
      });

      await handleDelete(mockData[0]);

      expect(mockConfirm).toHaveBeenCalled();
      expect(mockDeleteApi).toHaveBeenCalledWith(1);
      expect(mockFetchApi).toHaveBeenCalled(); // 刪除後重新獲取數據
    });

    it('handleBatchDelete 應該批量刪除項目', async () => {
      // Mock ElMessageBox.confirm
      const mockConfirm = vi.fn().mockResolvedValue('confirm');
      vi.stubGlobal('ElMessageBox', { confirm: mockConfirm });

      const { selectedRows, handleSelectionChange, handleBatchDelete } = useTable({
        fetchApi: mockFetchApi,
        deleteApi: mockDeleteApi,
        immediate: false,
      });

      // 先選中一些項目
      handleSelectionChange([mockData[0], mockData[2]]);

      await handleBatchDelete();

      expect(mockConfirm).toHaveBeenCalled();
      expect(mockDeleteApi).toHaveBeenCalledTimes(2);
      expect(mockDeleteApi).toHaveBeenCalledWith(1);
      expect(mockDeleteApi).toHaveBeenCalledWith(3);
      expect(selectedRows.value).toEqual([]); // 刪除後清空選中項
    });

    it('沒有選中項時 handleBatchDelete 應該顯示警告', async () => {
      const mockWarning = vi.fn();
      vi.stubGlobal('ElMessage', { warning: mockWarning });

      const { handleBatchDelete } = useTable({
        fetchApi: mockFetchApi,
        deleteApi: mockDeleteApi,
        immediate: false,
      });

      await handleBatchDelete();

      expect(mockWarning).toHaveBeenCalledWith('請先選擇要刪除的記錄');
      expect(mockDeleteApi).not.toHaveBeenCalled();
    });
  });

  describe('計算屬性', () => {
    it('hasData 應該正確反映是否有數據', () => {
      const { data, hasData } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      expect(hasData.value).toBe(false);

      data.value = mockData;
      expect(hasData.value).toBe(true);
    });

    it('isEmpty 應該正確反映是否為空', () => {
      const { data, loading, isEmpty } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      expect(isEmpty.value).toBe(true);

      loading.value = true;
      expect(isEmpty.value).toBe(false); // 加載中不算空

      loading.value = false;
      data.value = mockData;
      expect(isEmpty.value).toBe(false);
    });

    it('hasSelection 應該正確反映是否有選中項', () => {
      const { selectedRows, hasSelection, handleSelectionChange } = useTable({
        fetchApi: mockFetchApi,
        immediate: false,
      });

      expect(hasSelection.value).toBe(false);

      handleSelectionChange([mockData[0]]);
      expect(hasSelection.value).toBe(true);
    });
  });
});
