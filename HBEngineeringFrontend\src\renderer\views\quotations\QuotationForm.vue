<template>
  <div class="quotation-form">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? '編輯報價單' : '創建報價單' }}</span>
          <el-button @click="$router.back()">返回</el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px">
        
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客戶" prop="customer_id">
              <el-select v-model="form.customer_id" placeholder="請選擇客戶" filterable>
                <el-option
                  v-for="customer in customers"
                  :key="customer.id"
                  :label="customer.name"
                  :value="customer.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期至" prop="valid_until">
              <el-date-picker
                v-model="form.valid_until"
                type="date"
                placeholder="選擇有效期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 產品項目 -->
        <el-form-item label="產品項目">
          <el-table :data="form.items" style="width: 100%">
            <el-table-column label="產品" width="200">
              <template #default="scope">
                <el-select
                  v-model="scope.row.product_id"
                  placeholder="選擇產品"
                  @change="handleProductChange(scope.$index, scope.row.product_id)">
                  <el-option
                    v-for="product in products"
                    :key="product.id"
                    :label="product.name"
                    :value="product.id" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="描述" width="200">
              <template #default="scope">
                <el-input v-model="scope.row.description" placeholder="輸入描述" />
              </template>
            </el-table-column>
            <el-table-column label="數量" width="120">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.quantity"
                  :min="1"
                  @change="calculateAmount(scope.$index)" />
              </template>
            </el-table-column>
            <el-table-column label="單價" width="120">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.unit_price"
                  :precision="2"
                  :min="0"
                  @change="calculateAmount(scope.$index)" />
              </template>
            </el-table-column>
            <el-table-column label="金額" width="120">
              <template #default="scope">
                <span>${{ scope.row.amount.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeItem(scope.$index)">
                  刪除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div style="margin-top: 10px;">
            <el-button type="primary" @click="addItem">添加項目</el-button>
          </div>
        </el-form-item>

        <!-- 金額計算 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="折扣率(%)" prop="discount_rate">
              <el-input-number
                v-model="form.discount_rate"
                :precision="2"
                :min="0"
                :max="100"
                @change="calculateTotal" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="備註">
              <el-input
                v-model="form.notes"
                type="textarea"
                :rows="3"
                placeholder="輸入備註" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 金額摘要 -->
        <el-card class="amount-summary">
          <div class="amount-row">
            <span>小計：</span>
            <span>${{ form.total_amount.toFixed(2) }}</span>
          </div>
          <div class="amount-row">
            <span>折扣：</span>
            <span>-${{ form.discount_amount.toFixed(2) }}</span>
          </div>
          <div class="amount-row total">
            <span>總計：</span>
            <span>${{ form.final_amount.toFixed(2) }}</span>
          </div>
        </el-card>

        <!-- 提交按鈕 -->
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '創建' }}
          </el-button>
          <el-button @click="$router.back()">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

const route = useRoute();
const router = useRouter();

const isEdit = computed(() => !!route.params.id);
const formRef = ref();
const submitting = ref(false);

// 數據
const customers = ref([]);
const products = ref([]);

// 表單數據
const form = reactive({
  customer_id: '',
  valid_until: '',
  discount_rate: 0,
  notes: '',
  items: [],
  total_amount: 0,
  discount_amount: 0,
  final_amount: 0
});

// 驗證規則
const rules = {
  customer_id: [
    { required: true, message: '請選擇客戶', trigger: 'change' }
  ],
  valid_until: [
    { required: true, message: '請選擇有效期', trigger: 'change' }
  ]
};

// 添加項目
const addItem = () => {
  form.items.push({
    product_id: '',
    description: '',
    quantity: 1,
    unit_price: 0,
    amount: 0
  });
};

// 刪除項目
const removeItem = (index) => {
  form.items.splice(index, 1);
  calculateTotal();
};

// 產品變更處理
const handleProductChange = (index, productId) => {
  const product = products.value.find(p => p.id === productId);
  if (product) {
    form.items[index].description = product.description;
    form.items[index].unit_price = product.unit_price;
    calculateAmount(index);
  }
};

// 計算單項金額
const calculateAmount = (index) => {
  const item = form.items[index];
  item.amount = item.quantity * item.unit_price;
  calculateTotal();
};

// 計算總金額
const calculateTotal = () => {
  form.total_amount = form.items.reduce((sum, item) => sum + item.amount, 0);
  form.discount_amount = form.total_amount * (form.discount_rate / 100);
  form.final_amount = form.total_amount - form.discount_amount;
};

// 獲取客戶列表
const fetchCustomers = async () => {
  // 模擬數據
  customers.value = [
    { id: 1, name: 'ABC電子有限公司' },
    { id: 2, name: 'XYZ工程公司' },
    { id: 3, name: '香港電器有限公司' }
  ];
};

// 獲取產品列表
const fetchProducts = async () => {
  // 模擬數據
  products.value = [
    { id: 1, name: 'LED燈具', description: '高效能LED照明設備', unit_price: 150.00 },
    { id: 2, name: '電纜線', description: '高品質電纜線材', unit_price: 25.50 },
    { id: 3, name: '開關插座', description: '標準電器開關插座', unit_price: 35.00 }
  ];
};

// 提交表單
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    submitting.value = true;
    
    // 模擬API調用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success(isEdit.value ? '更新成功' : '創建成功');
    router.push('/quotations');
    
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message);
    }
  } finally {
    submitting.value = false;
  }
};

onMounted(() => {
  fetchCustomers();
  fetchProducts();
  
  // 初始化一個空項目
  if (!isEdit.value) {
    addItem();
  }
});
</script>

<style scoped>
.quotation-form {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-summary {
  margin: 20px 0;
  max-width: 300px;
  margin-left: auto;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.amount-row.total {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
  border-top: 1px solid #ddd;
  padding-top: 10px;
}
</style> 